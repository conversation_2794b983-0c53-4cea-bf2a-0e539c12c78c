# Sparrow Shared Jenkins Library #

Shared libraries in Jenkins Pipelines are reusable pieces of code that can be organized into functions and classes. These libraries allow you to encapsulate common logic, making it easier to maintain and share across multiple pipelines and projects. By utilizing shared libraries, you can enhance the modularity, reusability, and maintainability of your Jenkins Pipeline scripts.

## Directory structure ##
The directory structure of a Shared Library repository is as follows:

```sh 
(root)
+- src                     # The src dir contains the actual Groovy Scripts that make up the shared Library
|   +- org                 # ( Org ) Seperate Folder SurveySparrow , ThriveSparrow and SparrowDesk 
|       +- Fun             # ( Fun ) Function Name
|           +- Bar.groovy  # Actual Shared Library .groovy Files for the Function Name
|
+- vars                    # The vars dir contains global variables that are defined in the shared Library
|   +- foo.groovy          
|   +- foo.txt            
|           
+- resources               # The resources dir conatins any static resources that are used by the shared library in the src dir Eg: Pyhton, json, yml , bash and etc
|   +- org
|       +- Fun
|           +- bar.json    # static config helper data for org.Fun.Bar
|           +- bar.py      # static python helper for org.Fun.Bar
|
```

## Configuring Shared Library in Jenkins ##

You can configure the Jenkins Shared Library in you Staging and Production Jenkins and start using them in your pipelines

Step 1: Install the plugin Pipeline: Groovy Libraries

Step 2: Go to Manage Jenkins → Configure System

Step 3: Search for Global Trusted Pipeline Libraries and configure the parameters.

- Name: "SS-Shared-Lib"
- Default Version: Master ( The Repo branch which has the correct version )
- Load implicitly ( Leave unchecked as we can manually import the Lib where its needed or else it will be loading in all the pipelines even if not used )
- Allow default version to be overridden ( Leave checked in staging and Unchecked in Production )
- Cache fetched versions on controller for quick retrieval ( Check this option add the nessacary refresh min )
- Retrieval method ( Use Modern SCM and add the current Git Details Enter the Repo URl and Cred Parameter only )

For Testing Purpose you can call the library as =='@Library(“SS-Shared-Lib@master”) _'== you will import that version in the pipeline

## Using Library in you Jenkins Pipelines ##

After you finish the configuring process you can start using them in your pipeines

```sh

@Library("SS-Shared-Lib") _
/* Using a version specifier, such as branch, tag, etc */
@Library("SS-Shared-Lib@1.0") _
/* Accessing multiple libraries with one statement */
@Library(["SS-Shared-Lib", "otherlib@abc1234"]) _

```

## Creating Custom Steps

https://www.jenkins.io/doc/book/pipeline/shared-libraries/#defining-custom-steps

# Using the shared libraries across orgs

## Constants and Dynamic variables
- Global CommonConstants
  Common constants across the entire org like default region, email and username of common account to be placed as a class in `src/org/global/CommonConstants.groovy`
- Org Level CommonConstants
  Common constants across the product across environments like constants across staging and production of a particular product should be placed in `src/org/{orgName}/CommonConstants.groovy`
- Org Level Dynamic constants
  Constants which are dynamic and changes based on environment should be placed in `resources/config/{orgName}/{environment}.json`

## Classes, methods and vars guidelines
This section specifies when to use `src/org/` and when to use `vars/`

### When to use vars
- vars should be used only when we are using pipeline specific steps in the methods. For example using git() step, or nodejs() step.
- These are pipeline specific steps which will be loaded when running the pipeline, so they will be available for use.
- The methods should be here only when they are making use of a pipeline step.

### When to use src
- Reusable complex logic should be inside src
- Try to use the src/ as much as possible, it will be compiled before executing the pipeline
- Try to code the logic as reusable as possible.
- It is always performant to run multiple sh lines in a single go, so if you are writing something that involves running an sh command, you can get the command built in src/ and run the command from a function in vars/

### Why not all the methods in vars
- The methods in `vars/` in Jenkins will be loaded to the environment, so if we overload everything to methods inside vars/ it will affect the performance of Jenkins master node
- all the groovy code will be executed in the master node even if agent is specified to run in a different worker node

## Sample Use case
- Sending notifications
- vars/
  - It should contain the method to send the notification since slackSend() is a pipeline step.
- src/
  - It should contain the logic to handle how the notification message is built.
- pipeline/
  - should call just notify() in vars/ with necessary parameters to send the notification

### Generating Documentation
- Documentation should be in JavaDoc style in all the files in src/ and vars/ and for all the methods.
- Documentation can be generated through the following command,
```
npm install
node documentGenerator.js /path/to/sharedlib
```
- This will generate the docs as html file in docs/ as index.html file, which can then be served.
