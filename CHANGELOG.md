# [1.0.0](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.9.13...v1.0.0) (2025-07-09)



## [0.9.13](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.9.12...v0.9.13) (2025-07-08)



## [0.9.12](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.9.11...v0.9.12) (2025-07-07)



## [0.9.11](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.9.10...v0.9.11) (2025-07-04)


### Bug Fixes

* **surveysparrow:** update namespace in Analyze backend configuration for production and staging ([440d582](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/commits/440d5823a6cad8afc0c7874181fc706684680256))



## [0.9.10](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.9.9...v0.9.10) (2025-07-04)



## [0.9.9](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.9.8...v0.9.9) (2025-07-04)



## [0.9.8](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.9.7...v0.9.8) (2025-06-30)



## [0.9.7](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.9.6...v0.9.7) (2025-06-30)



## [0.9.6](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.9.5...v0.9.6) (2025-06-30)


### Bug Fixes

* **bitbuckethelper:** rename reference argument for clarity ([e1be404](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/commits/e1be404e556ae846dd7457184820200c02b932bb))
* **bitbuckethelper:** update argument name from 'reference' to 'reference_branch' for improved clarity ([3d503a6](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/commits/3d503a6dafe434cbd564c7ae206205ab56e216a8))



## [0.9.5](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.9.4...v0.9.5) (2025-06-30)



## [0.9.4](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.9.3...v0.9.4) (2025-06-27)



## [0.9.3](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.9.2...v0.9.3) (2025-06-27)



## [0.9.2](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.9.1...v0.9.2) (2025-06-27)



## [0.9.1](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.9.0...v0.9.1) (2025-06-26)



# [0.9.0](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.8.1...v0.9.0) (2025-06-26)


### Features

* add KnowledgeHub configuration to production settings and CommonConstants ([4b07fdc](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/commits/4b07fdcec6f59b7566a37eda7e3d9058c0792ca4))



## [0.8.1](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.8.0...v0.8.1) (2025-06-24)


### Bug Fixes

* **CommonConstants:** correct frontend key structure for Attainment service configuration ([35d3be6](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/commits/35d3be65fd5b059137ad99d02d02c65b81ceead6))
* **CommonConstants:** enable migration in Attainment service configuration ([385c07a](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/commits/385c07afcad66b138b60260d5d3a8de3e50e6458))
* **CommonConstants:** update frontend configuration key for Attainment service ([d015844](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/commits/d015844f5405eaa94cfbc1ce3aab85344e066345))



# [0.8.0](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.7.2...v0.8.0) (2025-06-23)


### Features

* add Attainment service configuration to CommonConstants ([4c386a4](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/commits/4c386a4d2a8e0c38c0c09936922aa15973d5b2f9))
* add Attainment service configuration to production and staging settings ([3d2a002](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/commits/3d2a002f95bf68fdb26c16704d44c99683791a01))



## [0.7.2](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.7.1...v0.7.2) (2025-06-20)



## [0.7.1](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.7.0...v0.7.1) (2025-06-19)



# [0.7.0](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.6.2...v0.7.0) (2025-06-18)


### Bug Fixes

* update distFolder path and add nodeVersion in CommonConstants ([ae2336c](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/commits/ae2336c1e929e52a586dcd8b76b3a79884cc5713))


### Features

* add ASSET_BUCKET_NAME configuration for production and staging environments ([d88a326](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/commits/d88a32610271e58132e3da6e46e7cdc117356ad7))
* add SparrowPay configuration to CommonConstants ([ead839d](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/commits/ead839d3ae02a985bdd558d386bf6b72ebb69bea))
* add SparrowPay configuration to production and staging settings ([9a69639](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/commits/9a696393d90b20768eb4a0cccceeb5c56f9bc245))



## [0.6.2](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.6.1...v0.6.2) (2025-06-17)



## [0.6.1](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.6.0...v0.6.1) (2025-06-16)



# [0.6.0](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.5.1...v0.6.0) (2025-06-16)


### Bug Fixes

* lint errors ([25e8537](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/commits/25e8537d5b59f16953afbaa8bf526ca635542630))


### Features

* add reputation backend in ME Region ([d55fbbc](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/commits/d55fbbccd7f884e3d936fac2fd570c258d506fbc))
* add ReputationManagement in AP DC ([4988aff](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/commits/4988affc3f6dd2c88bdf7673e6aecaa02db53f2e))



## [0.5.1](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.5.0...v0.5.1) (2025-06-03)



# [0.5.0](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.4.0...v0.5.0) (2025-06-02)


### Features

* add NPM package configuration for Promptstack in production.json ([1e40c1a](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/commits/1e40c1a9dc70f5c585585e7e251338ed357e4861))



# [0.4.0](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.3.4...v0.4.0) (2025-05-30)


### Features

* add secret name for offline Android app in CommonConstants ([95aeaed](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/commits/95aeaed277ccb01d45c115856a52173d74cd91bf))



## [0.3.4](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.3.3...v0.3.4) (2025-05-29)



## [0.3.3](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.3.2...v0.3.3) (2025-05-29)


### Bug Fixes

* update Slack channel name in SSDocs configuration for staging ([d994cae](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/commits/d994cae42976737162b592100289c570c96c50c5))



## [0.3.2](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.3.1...v0.3.2) (2025-05-28)



## [0.3.1](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.3.0...v0.3.1) (2025-05-27)



# [0.3.0](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.2.4...v0.3.0) (2025-05-26)


### Bug Fixes

* lint errors ([e39c743](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/commits/e39c743303c9b9c80388d819209b9ce67b9c2c06))


### Features

* add configuration for Promptstack and SSDocs in production and staging environments ([15fbbbd](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/commits/15fbbbd829107422f17bdcf7d5a81ac80f1509b2))
* update Edith configuration to include admin-server ([0c7199f](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/commits/0c7199f58154f98c977d0ef102d51e13d99cb28f))



## [0.2.4](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.2.3...v0.2.4) (2025-05-23)


### Bug Fixes

* update Google config file copy command to include all files ([d443291](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/commits/d44329132130430a7af2140943ceeb928d149499))



## [0.2.3](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.2.2...v0.2.3) (2025-05-20)



## [0.2.2](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.2.1...v0.2.2) (2025-05-07)



## [0.2.1](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.2.0...v0.2.1) (2025-05-07)



# [0.2.0](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.1.4...v0.2.0) (2025-05-06)


### Bug Fixes

* add ArgoCD to config ([4d5490d](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/commits/4d5490d29c7db311c4e397011b9e312ba993da76))
* add autostash to rebase command ([f6b23a4](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/commits/f6b23a4249e654fc14d25974f50bb0957b35cb49))
* add the angular preset for recommended bump ([27cb2d0](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/commits/27cb2d0c8ef5d096bff50d25c1528b59e16e1125))
* return default tag as v1.0.0 ([be3e8ca](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/commits/be3e8ca978991bb6dd9e1de921278225637dc1b3))
* separate create and push tag methods ([ada204a](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/commits/ada204a5f0eb233d599371cf7eec80e88a341ef0))
* update getLatest tag method to use tag list. ([b556c0d](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/commits/b556c0d19587f8a5cab06a85b0657b1496f935f4))


### Features

* add all common devops repositories to common constants ([4ecb639](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/commits/4ecb63935b6f43476a911288cc759f236c95576f))
* add methods to create and push tags ([f770973](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/commits/f770973592e4f5c55f484c101d357189c8792477))
* add vars/ methods for latest tag ([b1cfcb2](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/commits/b1cfcb24eeb0a04ac1e28fb846fb46f13ae6f7a1))



## [0.1.4](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.1.3...v0.1.4) (2025-05-06)



## [0.1.3](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.1.2...v0.1.3) (2025-04-30)



## [0.1.2](https://bitbucket.org/surveysparrow/sparrow-jenkins-shared-library/compare/v0.1.0...v0.1.2) (2025-04-29)



# 0.1.0 (2025-04-24)



