{"extends": "recommended", "rules": {"ClassName": {"enabled": true, "severity": "error", "regex": "^[A-Z][a-zA-Z0-9]*$"}, "MethodName": {"enabled": true, "severity": "error", "regex": "^[a-z][a-zA-Z0-9]*$"}, "NoDef": {"enabled": false, "severity": "warning"}, "BracesForIfElse": {"enabled": true, "severity": "error"}, "CompileStatic": {"enabled": false}, "LineLength": {"enabled": true, "severity": "warning", "length": 150}, "TrailingWhitespace": {"enabled": true, "severity": "error"}, "Indentation": {"enabled": true, "severity": "error", "spacesPerIndentLevel": 2}, "DuplicateStringLiteral": {"enabled": true, "severity": "info"}, "UnusedImport": {"enabled": true, "severity": "error"}, "FileEndsWithoutNewline": {"enabled": true, "severity": "error"}, "Instanceof": {"enabled": false}, "MethodReturnTypeRequired": {"enabled": true, "severity": "warning"}}, "formatter": {"indentation": 2, "maxLineLength": 150, "bracesPlacement": "end_of_line", "spaceAroundOperators": true, "spaceAfterComma": true}, "ignore": ["**/build/**", "**/node_modules/**"]}