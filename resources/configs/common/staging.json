{"ACCOUNT_CONFIG": {"crossAccount": true, "roleArn": "arn:aws:iam::************:role/CrossAccountJenkinsWorker", "accountId": "************"}, "SS_STAGING_ACCOUNT_CONFIG": {"crossAccount": true, "roleArn": "arn:aws:iam::************:role/CrossAccountJenkinsRole", "accountId": "************"}, "FRONTEND_LABS": {"ARGOCD": {"username": "admin", "domain": "argocd.campaignsparrow.com", "ACTIONS": {"deploy": "KUSTOMIZE_DEPLOY", "restart": "RESTART", "configUpdate": "CONFIG_UPDATE"}}, "FRONTEND_LABS_API": {"repo": "*****************:surveysparrow/labs-apis.git", "configRepo": "*****************:surveysparrow/surveysparrow-staging-config.git", "ecrRepoUrl": "************.dkr.ecr.us-east-2.amazonaws.com", "deploymentsChannel": "surveysparrow-labs-deployments", "Common/Frontend-Labs/Frontend-Labs-Api": {"domain": "felabsapis.staging.sparrowapps.com", "argocdAppName": "frontend-labs-api-staging-us-east-2", "ecrRepo": "frontend-labs-api", "envFile": "surveysparrow-labs/frontend-api.env", "kustomizePath": "common/frontend-labs-api/kustomize/overlays/staging/us-east-2/kustomization.yaml"}}}}