{"ACCOUNT_CONFIG": {"crossAccount": true, "roleArn": "arn:aws:iam::************:role/CrossAccountJenkinsWorker", "accountId": "************"}, "FRONTEND_LABS": {"ARGOCD": {"username": "admin", "domain": "argocd.campaignsparrow.com", "ACTIONS": {"deploy": "KUSTOMIZE_DEPLOY", "restart": "RESTART", "configUpdate": "CONFIG_UPDATE"}}, "FRONTEND_LABS_API": {"repo": "*****************:surveysparrow/labs-apis.git", "configRepo": "*****************:surveysparrow/surveysparrow-production-config.git", "ecrRepoUrl": "************.dkr.ecr.us-east-2.amazonaws.com", "deploymentsChannel": "surveysparrow-labs-deployments", "Common/Frontend-Labs/Frontend-Labs-Api": {"domain": "felabsapis.platform.sparrowapps.com", "argocdAppName": "frontend-labs-api-production-us-east-2", "ecrRepo": "frontend-labs-api-us", "envFile": "surveysparrow-labs/frontend-labs-api/frontend-api.env", "kustomizePath": "common/frontend-labs-api/kustomize/overlays/production/us-east-2/kustomization.yaml"}}}}