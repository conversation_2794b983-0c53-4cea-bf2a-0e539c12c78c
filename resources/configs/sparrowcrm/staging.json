{"ACCOUNT_CONFIG": {"crossAccount": true, "roleArn": "arn:aws:iam::************:role/CrossAccountJenkinsWorker", "accountId": "************"}, "ECR_REGISTRY": {"us-east-1": "************.dkr.ecr.us-east-1.amazonaws.com"}, "CLOUDFLARE": {"pipelineName": "SparrowCrm/Website/CloudflareCachePurge", "baseDomain": "return ['www.sparrowcrmstaging.com']", "DOMAINS": {"www.sparrowcrmstaging.com": {"zoneId": "2fbc74df554e57e1f213e6afa86f9ab3"}}}, "WEBSITE": {"websiteRepo": "*****************:surveysparrow/crm-website.git", "configRepo": "*****************:surveysparrow/crm-staging-config.git", "ecrRepoUrl": "************.dkr.ecr.us-east-1.amazonaws.com", "websiteDeploymentsChannel": "sparrowcrm-website-deployment-status", "ARGOCD": {"username": "admin", "domain": "argocd.campaignsparrow.com", "ACTIONS": {"deploy": "KUSTOMIZE_DEPLOY", "restart": "RESTART", "configUpdate": "CONFIG_UPDATE"}}, "SparrowCrm/Website/SparrowCrmStaging/WebsiteDeployment": {"domain": "www.sparrowcrmstaging.com", "argocdAppName": "sparrowcrmstaging-website-us-east-1", "ecrRepo": "sparrowcrmstaging-website", "secretName": "sc_website", "envFile": "Website/sparrowcrmstaging/staging.env", "kustomizePath": "sparrowcrm/website/kustomize/overlays/staging/us-east-1/sparrowcrmstaging/kustomization.yaml"}}}