{"ACCOUNT_CONFIG": {"crossAccount": true, "roleArn": "arn:aws:iam::************:role/CrossAccountJenkinsWorker", "accountId": "************"}, "ECR_REGISTRY": {"us-east-1": "************.dkr.ecr.us-east-1.amazonaws.com"}, "CLOUDFLARE": {"pipelineName": "SparrowCrm/Website/CloudflareCachePurge", "baseDomain": "return ['www.sparrowcrm.com']", "DOMAINS": {"www.sparrowcrm.com": {"zoneId": "f05d6785b3986ba233bfa658c74e5cdf"}}}, "WEBSITE": {"websiteRepo": "*****************:surveysparrow/crm-website.git", "configRepo": "*****************:surveysparrow/crm-production-config.git", "ecrRepoUrl": "************.dkr.ecr.us-east-1.amazonaws.com", "websiteDeploymentsChannel": "sparrowcrm-website-deployment-status", "ARGOCD": {"username": "admin", "domain": "argocd.campaignsparrow.com", "ACTIONS": {"deploy": "KUSTOMIZE_DEPLOY", "restart": "RESTART", "configUpdate": "CONFIG_UPDATE"}}, "SparrowCrm/Website/SparrowCrm/WebsiteDeployment": {"domain": "www.sparrowcrm.com", "argocdAppName": "sparrowcrm-website-us-east-1", "ecrRepo": "sparrowcrm-website", "secretName": "sparrowcrm_website", "envFile": "Website/production.env", "kustomizePath": "sparrowcrm/website/kustomize/overlays/production/us-east-1/sparrowcrm/kustomization.yaml"}}}