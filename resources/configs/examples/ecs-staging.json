{"CONFIG_REPO": "*****************:surveysparrow/my-service-staging-config.git", "ECR_REGISTRY": {"us-east-1": "************.dkr.ecr.us-east-1.amazonaws.com"}, "ACCOUNT_CONFIG": {"crossAccount": true, "roleArn": "arn:aws:iam::************:role/CrossAccountJenkinsWorker", "accountId": "************", "duration": 1800}, "ECS": {"clusters": {"us-east-1": "staging-cluster-us-east-1"}, "services": {"api-service": {"taskDefinitionFamily": "api-service-staging", "desiredCount": 2, "cpu": "256", "memory": "512", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "subnets": {"us-east-1": ["subnet-staging-1", "subnet-staging-2"]}, "securityGroups": {"us-east-1": ["sg-api-staging"]}, "assignPublicIp": "ENABLED", "healthCheckGracePeriodSeconds": 180, "maxHealthyPercent": 200, "minHealthyPercent": 0, "deploymentStrategy": "ROLLING", "deploymentTimeoutMinutes": 10, "enableLogging": true, "logGroupName": "/ecs/api-service-staging", "logRetentionDays": 7, "executionRoleArn": "arn:aws:iam::************:role/ecsTaskExecutionRole", "taskRoleArn": "arn:aws:iam::************:role/apiServiceTaskRole", "environmentVariables": {"ENV": "staging", "LOG_LEVEL": "debug", "DATABASE_URL": "postgresql://staging-db:5432/api", "REDIS_URL": "redis://staging-redis:6379", "DEBUG": "true"}, "secrets": {"DATABASE_PASSWORD": "arn:aws:ssm:us-east-1:************:parameter/api-service-staging/database-password"}, "portMappings": [{"containerPort": 8080, "protocol": "tcp"}], "tags": {"Environment": "staging", "Service": "api-service", "Team": "backend"}}, "worker-service": {"taskDefinitionFamily": "worker-service-staging", "desiredCount": 1, "cpu": "256", "memory": "512", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "subnets": {"us-east-1": ["subnet-staging-1", "subnet-staging-2"]}, "securityGroups": {"us-east-1": ["sg-worker-staging"]}, "assignPublicIp": "ENABLED", "healthCheckGracePeriodSeconds": 120, "maxHealthyPercent": 200, "minHealthyPercent": 0, "deploymentStrategy": "ROLLING", "deploymentTimeoutMinutes": 8, "enableLogging": true, "logGroupName": "/ecs/worker-service-staging", "logRetentionDays": 3, "executionRoleArn": "arn:aws:iam::************:role/ecsTaskExecutionRole", "taskRoleArn": "arn:aws:iam::************:role/workerServiceTaskRole", "environmentVariables": {"ENV": "staging", "LOG_LEVEL": "debug", "QUEUE_URL": "https://sqs.us-east-1.amazonaws.com/************/staging-queue"}, "secrets": {"DATABASE_PASSWORD": "arn:aws:ssm:us-east-1:************:parameter/worker-service-staging/database-password"}, "tags": {"Environment": "staging", "Service": "worker-service", "Team": "backend"}}}, "loadBalancers": {"api-service": {"targetGroupArn": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/api-service-staging/1234567890123456", "containerName": "api-service", "containerPort": 8080}}}, "STAGING_ENVIRONMENTS": {"ALPHA": {"env": "alpha", "namespace": "alpha", "ecsCluster": "staging-cluster-us-east-1", "ecsServiceSuffix": "-alpha", "taskDefinitionSuffix": "-alpha"}, "BETA": {"env": "beta", "namespace": "beta", "ecsCluster": "staging-cluster-us-east-1", "ecsServiceSuffix": "-beta", "taskDefinitionSuffix": "-beta"}}, "NOTIFICATION": {"enabled": true, "channels": {"deployments": "#staging-deployments"}, "onSuccess": false, "onFailure": true, "onStart": false}, "MONITORING": {"cloudWatch": {"enabled": true, "dashboardName": "ECS-Staging-Services", "alarms": {"serviceUnhealthy": {"threshold": 1, "evaluationPeriods": 1, "period": 60}}}}, "SCALING": {"autoScaling": {"enabled": false}}}