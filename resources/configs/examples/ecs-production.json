{"CONFIG_REPO": "*****************:surveysparrow/my-service-production-config.git", "ECR_REGISTRY": {"us-east-1": "************.dkr.ecr.us-east-1.amazonaws.com", "eu-central-1": "************.dkr.ecr.eu-central-1.amazonaws.com"}, "ACCOUNT_CONFIG": {"crossAccount": true, "roleArn": "arn:aws:iam::************:role/CrossAccountJenkinsWorker", "accountId": "************", "duration": 3600}, "ECS": {"clusters": {"us-east-1": "production-cluster-us-east-1", "eu-central-1": "production-cluster-eu-central-1"}, "services": {"api-service": {"taskDefinitionFamily": "api-service-production", "desiredCount": 5, "cpu": "512", "memory": "1024", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "subnets": {"us-east-1": ["subnet-********", "subnet-********"], "eu-central-1": ["subnet-abcdef12", "subnet-21fedcba"]}, "securityGroups": {"us-east-1": ["sg-api-production-us"], "eu-central-1": ["sg-api-production-eu"]}, "assignPublicIp": "DISABLED", "healthCheckGracePeriodSeconds": 300, "maxHealthyPercent": 200, "minHealthyPercent": 50, "deploymentStrategy": "ROLLING", "deploymentTimeoutMinutes": 20, "enableLogging": true, "logGroupName": "/ecs/api-service-production", "logRetentionDays": 30, "executionRoleArn": "arn:aws:iam::************:role/ecsTaskExecutionRole", "taskRoleArn": "arn:aws:iam::************:role/apiServiceTaskRole", "environmentVariables": {"ENV": "production", "LOG_LEVEL": "info", "DATABASE_URL": "postgresql://prod-db:5432/api", "REDIS_URL": "redis://prod-redis:6379"}, "secrets": {"DATABASE_PASSWORD": "arn:aws:ssm:us-east-1:************:parameter/api-service/database-password", "API_SECRET_KEY": "arn:aws:secretsmanager:us-east-1:************:secret:api-service/secret-key"}, "portMappings": [{"containerPort": 8080, "protocol": "tcp"}], "tags": {"Environment": "production", "Service": "api-service", "Team": "backend", "CostCenter": "engineering"}}, "worker-service": {"taskDefinitionFamily": "worker-service-production", "desiredCount": 3, "cpu": "256", "memory": "512", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "subnets": {"us-east-1": ["subnet-********", "subnet-********"], "eu-central-1": ["subnet-abcdef12", "subnet-21fedcba"]}, "securityGroups": {"us-east-1": ["sg-worker-production-us"], "eu-central-1": ["sg-worker-production-eu"]}, "assignPublicIp": "DISABLED", "healthCheckGracePeriodSeconds": 180, "maxHealthyPercent": 150, "minHealthyPercent": 50, "deploymentStrategy": "ROLLING", "deploymentTimeoutMinutes": 15, "enableLogging": true, "logGroupName": "/ecs/worker-service-production", "logRetentionDays": 14, "executionRoleArn": "arn:aws:iam::************:role/ecsTaskExecutionRole", "taskRoleArn": "arn:aws:iam::************:role/workerServiceTaskRole", "environmentVariables": {"ENV": "production", "LOG_LEVEL": "info", "QUEUE_URL": "https://sqs.us-east-1.amazonaws.com/************/production-queue"}, "secrets": {"DATABASE_PASSWORD": "arn:aws:ssm:us-east-1:************:parameter/worker-service/database-password"}, "tags": {"Environment": "production", "Service": "worker-service", "Team": "backend", "CostCenter": "engineering"}}}, "loadBalancers": {"api-service": {"targetGroupArn": "arn:aws:elasticloadbalancing:us-east-1:************:targetgroup/api-service-prod/************3456", "containerName": "api-service", "containerPort": 8080}}}, "NOTIFICATION": {"enabled": true, "channels": {"deployments": "#production-deployments", "alerts": "#production-alerts"}, "onSuccess": true, "onFailure": true, "onStart": true}, "MONITORING": {"cloudWatch": {"enabled": true, "dashboardName": "ECS-Production-Services", "alarms": {"highCpuUtilization": {"threshold": 80, "evaluationPeriods": 2, "period": 300}, "highMemoryUtilization": {"threshold": 85, "evaluationPeriods": 2, "period": 300}, "serviceUnhealthy": {"threshold": 1, "evaluationPeriods": 1, "period": 60}}}}, "BACKUP": {"enabled": true, "taskDefinitionBackup": {"retentionDays": 90, "s3Bucket": "my-company-ecs-backups", "s3Prefix": "production/task-definitions"}}, "SCALING": {"autoScaling": {"enabled": true, "minCapacity": 2, "maxCapacity": 20, "targetCpuUtilization": 70, "targetMemoryUtilization": 80, "scaleOutCooldown": 300, "scaleInCooldown": 300}}}