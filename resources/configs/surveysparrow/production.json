{"ASSET_REGION_MAP": {"us-east-1": "https://asset.surveysparrow.com", "eu-central-1": "https://asset-eu-ff.surveysparrow.com", "ap-south-1": "https://assets-ap-mi.surveysparrow.com", "me-central-1": "https://assets-me-uae.surveysparrow.com", "eu-west-2": "https://assets-eu-ln.surveysparrow.com", "ap-southeast-2": "https://assets-ap-sy.surveysparrow.com", "ca-central-1": "https://assets-ca.surveysparrow.com"}, "DEFAULT_DEPLOYMENTS_CHANNEL": "#production-deployments", "ARTIFACTS_BUCKET": "ss-build-artifacts-prod", "REGIONS": ["us-east-1", "eu-central-1", "ap-south-1", "me-central-1", "eu-west-2", "ap-southeast-2", "ca-central-1"], "REGION_NAMES": ["US-VIRGINIA", "EU-FRANKFURT", "AP-MUMBAI", "ME-UAE", "UK-LONDON", "AU-SYDNEY", "CA-CANADA"], "LAYERS": {"application": ["application", "api", "eui", "reports"], "worker": ["worker", "hulk-worker", "notification-worker", "generic-worker", "cron-worker"]}, "REGION_APP_MAP": {"us-east-1": "ss-us-vi", "eu-central-1": "ss-eu-ff", "ap-south-1": "ss-ap-mi", "me-central-1": "ss-me-uae", "eu-west-2": "ss-eu-ln", "ap-southeast-2": "ss-ap-sy", "ca-central-1": "ss-ca"}, "CLUSTER_REGION_MAP": {"us-east-1": "ss-production-us", "eu-central-1": "ss-production-eu", "ap-south-1": "ss-production-ap", "me-central-1": "ss-production-me", "eu-west-2": "ss-production-eu-ln", "ca-central-1": "ss-production-ca", "ap-southeast-2": "ss-production-ap-sy"}, "PRISM": {"DOMAINS": {"us-east-1": "https://prism-us.surveysparrow.com", "eu-central-1": "https://prism-eu.surveysparrow.com", "ap-south-1": "https://prism-ap.surveysparrow.com", "eu-west-2": "https://prism-uk.surveysparrow.com", "me-central-1": "https://prism-me.surveysparrow.com", "ap-southeast-2": "https://prism-sy.surveysparrow.com", "ca-central-1": "https://prism-ca.surveysparrow.com"}, "HEADER": "X-Prism-Token"}, "PRODUCTION_RELEASE_BRANCH": "production", "PRODUCTION_BRANCH": "master", "CONFIG_REPO": {"repoUrl": "*****************:surveysparrow/surveysparrow-production-config.git", "folder": "surveysparrow-production-config"}, "KUBERNETES_REPO": {"repoUrl": "*****************:surveysparrow/surveysparrow-production-eks.git", "folder": "surveysparrow-production-eks"}, "APP_DOCKER_REGISTRY": {"us-east-1": "556123534406.dkr.ecr.us-east-1.amazonaws.com", "eu-central-1": "556123534406.dkr.ecr.eu-central-1.amazonaws.com", "ap-south-1": "556123534406.dkr.ecr.ap-south-1.amazonaws.com", "me-central-1": "556123534406.dkr.ecr.me-central-1.amazonaws.com", "eu-west-2": "556123534406.dkr.ecr.eu-west-2.amazonaws.com", "ap-southeast-2": "556123534406.dkr.ecr.ap-southeast-2.amazonaws.com", "ca-central-1": "556123534406.dkr.ecr.ca-central-1.amazonaws.com", "me-central2-b": "me-central2-docker.pkg.dev/surveysparrow-production/me-central2-b"}, "DEPLOYMENTS_CHANNEL_ID": "C03L60RCN0L", "App-v1": {"repoUrl": "*****************:surveysparrow/app-v1.git", "folderName": "app-v1", "shortName": "app-v1", "service": "app-v1", "namespace": "surveys<PERSON>row", "dcLayerMap": {"us-east-1": {"appLayers": ["application", "api", "reports", "eui"], "workerLayers": ["hulk-worker", "notification-worker", "generic-worker", "cron-worker"]}, "eu-central-1": {"appLayers": ["application", "api", "reports", "eui"], "workerLayers": ["hulk-worker", "notification-worker", "generic-worker", "cron-worker"]}, "ap-south-1": {"appLayers": ["application", "api", "reports", "eui"], "workerLayers": ["hulk-worker", "notification-worker", "generic-worker", "cron-worker"]}, "me-central-1": {"appLayers": ["application", "api", "reports", "eui"], "workerLayers": ["hulk-worker", "notification-worker", "generic-worker", "cron-worker"]}, "eu-west-2": {"appLayers": ["application"], "workerLayers": ["worker"]}, "ap-southeast-2": {"appLayers": ["application"], "workerLayers": ["worker"]}, "ca-central-1": {"appLayers": ["application"], "workerLayers": ["worker"]}, "me-central2-b": {"appLayers": ["application"], "workerLayers": ["worker"]}}, "dcJobNamesMap": {"US-VIRGINIA": "App-v1/Deployments/US-VIRGINIA", "EU-FRANKFURT": "App-v1/Deployments/EU-FRANKFURT", "AP-MUMBAI": "App-v1/Deployments/AP-MUMBAI", "ME-UAE": "App-v1/Deployments/ME-UAE", "UK-LONDON": "App-v1/Deployments/UK-LONDON", "AU-SYDNEY": "App-v1/Deployments/AU-SYDNEY", "CA-CANADA": "App-v1/Deployments/CA-CANADA"}, "automation": {"jobName": "Qa-Automation-Testing", "dcOptionsMap": {"US-VIRGINIA": "surveysparrow_us_dc", "EU-FRANKFURT": "surveysparrow_eu_dc", "AP-MUMBAI": "surveysparrow_ap_dc", "ME-UAE": "surveysparrow_me_dc", "UK-LONDON": "surveysparrow_uk_dc", "AU-SYDNEY": "surveysparrow_sy_dc", "CA-CANADA": "surveysparrow_ca_dc"}}}, "SOC_JOB_NAMES_MAP": {"EUI": {"backend": {"US": "MicroServices/EUI/backend/Deployments/US", "EU": "MicroServices/EUI/backend/Deployments/EU", "AP": "MicroServices/EUI/backend/Deployments/AP", "ME": "MicroServices/EUI/backend/Deployments/ME", "UK": "MicroServices/EUI/backend/Deployments/UK"}, "frontend": {"US": "MicroServices/EUI/frontend/Deployments/US", "EU": "MicroServices/EUI/frontend/Deployments/EU", "AP": "MicroServices/EUI/frontend/Deployments/AP", "ME": "MicroServices/EUI/frontend/Deployments/ME", "UK": "MicroServices/EUI/frontend/Deployments/UK"}}, "ReputationManagement": {"backend": {"US": "MicroServices/ReputationManagement/backend/Deployments/US", "EU": "MicroServices/ReputationManagement/backend/Deployments/EU"}}, "TicketManagement": {"backend": {"US": "MicroServices/TicketManagement/backend/Deployments/US", "EU": "MicroServices/TicketManagement/backend/Deployments/EU", "AP": "MicroServices/TicketManagement/backend/Deployments/AP", "ME": "MicroServices/TicketManagement/backend/Deployments/ME", "UK": "MicroServices/TicketManagement/backend/Deployments/UK", "SY": "MicroServices/TicketManagement/backend/Deployments/SY", "CA": "MicroServices/TicketManagement/backend/Deployments/CA", "KSA": "MicroServices/TicketManagement/backend/Deployments/KSA"}}, "Integrations": {"backend": {"US": "MicroServices/Integrations/backend/Deployments/US", "EU": "MicroServices/Integrations/backend/Deployments/EU", "AP": "MicroServices/Integrations/backend/Deployments/AP", "ME": "MicroServices/Integrations/backend/Deployments/ME", "UK": "MicroServices/Integrations/backend/Deployments/UK", "SY": "MicroServices/Integrations/backend/Deployments/SY", "CA": "MicroServices/Integrations/backend/Deployments/CA", "KSA": "MicroServices/Integrations/backend/Deployments/KSA"}}}, "ReputationManagement": {"backend": {"repoUrl": "*****************:surveysparrow/ss-reputation-backend.git", "folderName": "ss-reputation-backend", "shortName": "reputation-backend", "service": "ss-reputation-backend", "namespace": "surveys<PERSON>row", "migrationsEnabled": false, "dcLayerMap": {"us-east-1": {"appLayers": ["application"], "workerLayers": ["worker"]}, "eu-central-1": {"appLayers": ["application"], "workerLayers": ["worker"]}}, "dcJobNamesMap": {"US-VIRGINIA": "MicroServices/ReputationManagement/backend/Deployments/US-VIRGINIA", "EU-FRANKFURT": "MicroServices/ReputationManagement/backend/Deployments/EU-FRANKFURT"}}}, "TicketManagement": {"backend": {"repoUrl": "*****************:surveysparrow/ss-ticket-backend.git", "folderName": "ss-ticket-backend", "shortName": "ticket-backend", "service": "ss-ticket-backend", "namespace": "surveys<PERSON>row", "migrationsEnabled": true, "dcLayerMap": {"us-east-1": {"appLayers": ["application"], "workerLayers": ["worker"]}, "eu-central-1": {"appLayers": ["application"], "workerLayers": ["worker"]}, "ap-south-1": {"appLayers": ["application"], "workerLayers": ["worker"]}, "me-central-1": {"appLayers": ["application"], "workerLayers": ["worker"]}, "eu-west-2": {"appLayers": ["application"], "workerLayers": ["worker"]}, "ap-southeast-2": {"appLayers": ["application"], "workerLayers": ["worker"]}, "ca-central-1": {"appLayers": ["application"], "workerLayers": ["worker"]}}, "dcJobNamesMap": {"US-VIRGINIA": "MicroServices/TicketManagement/backend/Deployments/US-VIRGINIA", "EU-FRANKFURT": "MicroServices/TicketManagement/backend/Deployments/EU-FRANKFURT", "AP-MUMBAI": "MicroServices/TicketManagement/backend/Deployments/AP-MUMBAI", "ME-UAE": "MicroServices/TicketManagement/backend/Deployments/ME-UAE", "UK-LONDON": "MicroServices/TicketManagement/backend/Deployments/UK-LONDON", "AU-SYDNEY": "MicroServices/TicketManagement/backend/Deployments/AU-SYDNEY", "CA-CANADA": "MicroServices/TicketManagement/backend/Deployments/CA-CANADA"}}}, "Integrations": {"backend": {"repoUrl": "*****************:surveysparrow/ss-integrations-backend.git", "folderName": "ss-integrations-backend", "shortName": "integrations-backend", "service": "ss-integrations-backend", "namespace": "surveys<PERSON>row", "dcLayerMap": {"us-east-1": {"appLayers": ["application", "api"], "workerLayers": ["worker"]}, "eu-central-1": {"appLayers": ["application", "api"], "workerLayers": ["worker"]}, "ap-south-1": {"appLayers": ["application", "api"], "workerLayers": ["worker"]}, "me-central-1": {"appLayers": ["application"], "workerLayers": ["worker"]}, "eu-west-2": {"appLayers": ["application"], "workerLayers": ["worker"]}, "ap-southeast-2": {"appLayers": ["application"], "workerLayers": ["worker"]}, "ca-central-1": {"appLayers": ["application"], "workerLayers": ["worker"]}}, "dcJobNamesMap": {"US-VIRGINIA": "MicroServices/Integrations/backend/Deployments/US-VIRGINIA", "EU-FRANKFURT": "MicroServices/Integrations/backend/Deployments/EU-FRANKFURT", "AP-MUMBAI": "MicroServices/Integrations/backend/Deployments/AP-MUMBAI", "ME-UAE": "MicroServices/Integrations/backend/Deployments/ME-UAE", "UK-LONDON": "MicroServices/Integrations/backend/Deployments/UK-LONDON", "AU-SYDNEY": "MicroServices/Integrations/backend/Deployments/AU-SYDNEY", "CA-CANADA": "MicroServices/Integrations/backend/Deployments/CA-CANADA"}}}, "EUI": {"backend": {"repoUrl": "*****************:surveysparrow/ss-eui-backend.git", "folderName": "ss-eui-backend", "shortName": "eui-backend", "service": "ss-eui-backend", "namespace": "surveys<PERSON>row", "migrationsEnabled": false, "dcLayerMap": {"us-east-1": {"appLayers": ["application"], "workerLayers": ["worker"]}, "eu-central-1": {"appLayers": ["application"], "workerLayers": ["worker"]}, "ap-south-1": {"appLayers": ["application"], "workerLayers": ["worker"]}, "me-central-1": {"appLayers": ["application"], "workerLayers": ["worker"]}, "eu-west-2": {"appLayers": ["application"], "workerLayers": ["worker"]}, "ap-southeast-2": {"appLayers": ["application"], "workerLayers": ["worker"]}, "ca-central-1": {"appLayers": ["application"], "workerLayers": ["worker"]}}, "dcJobNamesMap": {"US-VIRGINIA": "MicroServices/EUI/backend/Deployments/US-VIRGINIA", "EU-FRANKFURT": "MicroServices/EUI/backend/Deployments/EU-FRANKFURT", "AP-MUMBAI": "MicroServices/EUI/backend/Deployments/AP-MUMBAI", "ME-UAE": "MicroServices/EUI/backend/Deployments/ME-UAE", "UK-LONDON": "MicroServices/EUI/backend/Deployments/UK-LONDON", "AU-SYDNEY": "MicroServices/EUI/backend/Deployments/AU-SYDNEY", "CA-CANADA": "MicroServices/EUI/backend/Deployments/CA-CANADA"}}, "frontend": {"repoUrl": "*****************:surveysparrow/ss-eui-frontend.git", "folderName": "ss-eui-frontend", "shortName": "eui-frontend", "service": "ss-eui-frontend", "namespace": "surveys<PERSON>row", "validationPath": "/config-map", "cmName": "eui-dist-version", "cmKey": "euiDistVersion", "backendDeployment": "eui-backend", "backendDeploymentNameSuffix": "eui-backend-application", "versionKey": "euiDistVersion", "dcPrismToken": {"us-east-1": "PRISM_RIONINFDINIONFDIONDFINOM", "ap-south-1": "PRISM_RNFIFIONSFOINVIODVTTRPP", "eu-central-1": "PRISM_VUIBNTVIOTNVOINTVIZONTR", "eu-west-2": "PRISM_VFOVNZIONZIONZINZOOPKKS", "me-central-1": "PRISM_RMZOPZMOPMVXOINXIOEOEPL", "ap-southeast-2": "PRISM_H3RGV093H09GH3409HB3B09", "ca-central-1": "PRISM_345B09H53490HNB3V90R339"}, "dcJobNamesMap": {"US-VIRGINIA": "MicroServices/EUI/frontend/Deployments/US-VIRGINIA", "EU-FRANKFURT": "MicroServices/EUI/frontend/Deployments/EU-FRANKFURT", "AP-MUMBAI": "MicroServices/EUI/frontend/Deployments/AP-MUMBAI", "ME-UAE": "MicroServices/EUI/frontend/Deployments/ME-UAE", "UK-LONDON": "MicroServices/EUI/frontend/Deployments/UK-LONDON", "AU-SYDNEY": "MicroServices/EUI/frontend/Deployments/AU-SYDNEY", "CA-CANADA": "MicroServices/EUI/frontend/Deployments/CA-CANADA"}}}, "ARGOCD_DOMAIN": "argocd.surveysparrow.com", "PLATFORMS_CONFIG": {"ACCOUNT_ID": "************", "ARGOCD_DOMAIN": "platform-argocd.surveysparrow.com", "CLUSTER_NAME": {"us-east-1": "platform-production-cluster", "ap-south-1": "platform-production-cluster", "eu-central-1": "platform-production-eu", "me-central-1": "platform-production-me"}, "REGISTRY_ENDPOINT": {"us-east-1": "************.dkr.ecr.us-east-1.amazonaws.com", "eu-central-1": "************.dkr.ecr.eu-central-1.amazonaws.com", "ap-south-1": "************.dkr.ecr.ap-south-1.amazonaws.com", "me-central-1": "************.dkr.ecr.me-central-1.amazonaws.com"}}, "PLATFORMS_APP_CONFIG": {"Edith": {"slackChannel": "edith-production-deployment"}, "Shield": {"slackChannel": "shield-production-deployment", "dcLayerMap": {"US-VIRGINIA": ["application"], "EU-FRANKFURT": ["application"], "AP-MUMBAI": ["application"], "ME-UAE": ["application"]}}, "Blink": {"slackChannel": "blink-production-deployment", "dcLayerMap": {"US-VIRGINIA": ["application"], "EU-FRANKFURT": ["application"], "AP-MUMBAI": ["application"], "ME-UAE": ["application"]}}}, "CLOUDFLARE": {"ACCOUNT_ID": "bc116b8fc67ee5bbfe6bea301b37c531", "WEBSITES": {"EdithDocs": {"slackChannel": "edith-production-deployment", "production": {"domain": "docs.sparrowmailer.com", "envs": {"SITE_DOMAIN": "https://api.sparrowmailer.com", "API_URL": "https://api.sparrowmailer.com"}, "projectName": "edith-docs-<PERSON><PERSON><PERSON>"}}}}, "MOBILE": {"FASTLANE": {"AndroidOffline": {"backendEnv": {"production": "Release"}, "lanes": ["android production"]}, "IOSOffline": {"backendScheme": ["SurveySparrow Production Build"], "workflowIds": ["react-native-ios-prod"]}}}}