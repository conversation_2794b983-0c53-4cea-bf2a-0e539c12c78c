{"ASSET_REGION_MAP": {"us-east-1": "https://staging-assets.surveysparrow.com"}, "ASSET_BUCKET_NAME": "staging-assets.surveysparrow.com", "DEFAULT_DEPLOYMENTS_CHANNEL": "#staging-deployments", "ARTIFACTS_BUCKET": "ss-node-modules-staging", "REGIONS": ["us-east-1"], "REGION_NAMES": ["US-VIRGINIA"], "LAYERS": {"application": ["application", "api", "eui", "reports"], "worker": ["worker", "hulk-worker", "notification-worker", "generic-worker", "cron-worker"]}, "REGION_APP_MAP": {"us-east-1": "salesparrow"}, "CLUSTER_REGION_MAP": {"us-east-1": "ss-staging-cluster"}, "DEFAULT_AWS_ACCOUNT_ID": "************", "PRISM": {"DOMAINS": {"us-east-1": "https://prism-us.salesparrow.com"}, "HEADER": "X-Prism-Token"}, "PRODUCTION_RELEASE_BRANCH": "preproduction", "PRODUCTION_BRANCH": "preproduction-master", "CONFIG_REPO": {"repoUrl": "*****************:surveysparrow/surveysparrow-staging-config.git", "folder": "surveysparrow-staging-config"}, "PRODUCTION_CONFIG_REPO": {"repoUrl": "*****************:surveysparrow/surveysparrow-production-config.git", "folder": "surveysparrow-production-config"}, "KUBERNETES_REPO_NEW": {"repoUrl": "*****************:surveysparrow/sparrow-k8s-manifests.git", "folder": "sparrow-k8s-manifests"}, "KUBERNETES_REPO": {"repoUrl": "*****************:surveysparrow/surveysparrow-staging-eks.git", "folder": "surveysparrow-staging-eks"}, "APP_DOCKER_REGISTRY": {"us-east-1": "************.dkr.ecr.us-east-1.amazonaws.com"}, "DEPLOYMENTS_CHANNEL_ID": "C0533H43TE0", "DEPLOYED_SOCS": ["ReputationManagement", "TicketManagement", "EUI", "Integrations", "Analyze"], "App-v1": {"repoUrl": "*****************:surveysparrow/app-v1.git", "folderName": "app-v1", "shortName": "app-v1", "service": "app-v1", "namespace": "salesparrow", "defaultLayers": ["application", "worker"], "dcLayerMap": {"us-east-1": {"appLayers": ["application", "api", "reports", "eui"], "workerLayers": ["hulk-worker", "notification-worker", "generic-worker", "cron-worker"]}}, "dcJobNamesMap": {"US-VIRGINIA": "App-v1/Deployments/US-VIRGINIA"}}, "SOC_JOB_NAMES_MAP": {"EUI": {"backend": {"US": "MicroServices/EUI/backend/Deployments/US"}, "frontend": {"US": "MicroServices/EUI/frontend/Deployments/US"}}, "ReputationManagement": {"backend": {"US": "MicroServices/ReputationManagement/backend/Deployments/US"}}, "TicketManagement": {"backend": {"US": "MicroServices/TicketManagement/backend/Deployments/US"}}, "Integrations": {"backend": {"US": "MicroServices/Integrations/backend/Deployments/US"}}}, "ReputationManagement": {"backend": {"repoUrl": "*****************:surveysparrow/ss-reputation-backend.git", "folderName": "ss-reputation-backend", "shortName": "reputation-backend", "service": "ss-reputation-backend", "namespace": "salesparrow", "migrationsEnabled": false, "defaultLayers": ["application"], "dcLayerMap": {"us-east-1": {"appLayers": ["application"], "workerLayers": ["worker"]}}, "dcJobNamesMap": {"US-VIRGINIA": "MicroServices/ReputationManagement/backend/Deployments/US-VIRGINIA"}}}, "TicketManagement": {"backend": {"repoUrl": "*****************:surveysparrow/ss-ticket-backend.git", "folderName": "ss-ticket-backend", "shortName": "ticket-backend", "service": "ss-ticket-backend", "namespace": "salesparrow", "migrationsEnabled": true, "defaultLayers": ["application"], "dcLayerMap": {"us-east-1": {"appLayers": ["application"], "workerLayers": ["worker"]}}, "dcJobNamesMap": {"US-VIRGINIA": "MicroServices/TicketManagement/backend/Deployments/US-VIRGINIA"}}}, "Integrations": {"backend": {"repoUrl": "*****************:surveysparrow/ss-integrations-backend.git", "folderName": "ss-integrations-backend", "shortName": "integrations-backend", "service": "ss-integrations-backend", "namespace": "salesparrow", "migrationsEnabled": false, "defaultLayers": ["application"], "dcLayerMap": {"us-east-1": {"appLayers": ["application", "api"], "workerLayers": ["worker"]}}, "dcJobNamesMap": {"US-VIRGINIA": "MicroServices/Integrations/backend/Deployments/US-VIRGINIA"}}}, "EUI": {"backend": {"repoUrl": "*****************:surveysparrow/ss-eui-backend.git", "folderName": "ss-eui-backend", "shortName": "eui-backend", "service": "ss-eui-backend", "namespace": "salesparrow", "migrationsEnabled": false, "separateFrontend": true, "defaultLayers": ["application"], "dcLayerMap": {"us-east-1": {"appLayers": ["application"], "workerLayers": ["worker"]}}, "dcJobNamesMap": {"US-VIRGINIA": "MicroServices/EUI/backend/Deployments/US-VIRGINIA"}}, "frontend": {"repoUrl": "*****************:surveysparrow/ss-eui-frontend.git", "folderName": "ss-eui-frontend", "shortName": "eui-frontend", "service": "ss-eui-frontend", "namespace": "salesparrow", "validationPath": "/config-map", "cmName": "eui-dist-version", "cmKey": "euiDistVersion", "backendDeployment": "eui-backend", "backendDeploymentNameSuffix": "eui-backend-application", "versionKey": "euiDistVersion", "dcPrismToken": {"us-east-1": "TOKEN_FDINDFIONDIOINROINODINO"}, "minPods": {"us-east-1": 2}, "dcJobNamesMap": {"US-VIRGINIA": "MicroServices/EUI/frontend/Deployments/US-VIRGINIA"}}}, "Analyze": {"backend": {"namespace": "salesparrow", "defaultLayers": ["application"], "dcLayerMap": {"us-east-1": {"appLayers": ["application"], "workerLayers": ["worker", "beast-worker"]}}, "dcJobNamesMap": {"US-VIRGINIA": "MicroServices/Analyze/backend/Deployments/US-VIRGINIA"}}}, "PLATFORMS_CONFIG": {"ACCOUNT_ID": "************", "ARGOCD_DOMAIN": "deployments.helpsparrow.com", "CLUSTER_NAME": {"us-east-1": "platform-staging-cluster"}, "REGISTRY_ENDPOINT": {"us-east-1": "************.dkr.ecr.us-east-1.amazonaws.com"}}, "PLATFORMS_SANDBOX": {"Edith": {"iamRoleName": "EdithServiceSandboxRole", "configGroups": ["edith-api-server", "edith-worker", "edith-admin-server"], "envLayerMap": {"typesparrow": ["api-server", "worker", "admin-server"]}}, "SparrowPay": {"iamRoleName": "SparrowPayServiceRole", "configGroups": ["gateway", "admin-backend", "notifier", "payments", "webhooks"], "envLayerMap": {"helpsparrow": ["gateway", "admin-backend", "notifier", "payments", "webhooks"], "wingssparrow": ["gateway", "admin-backend", "notifier", "payments", "webhooks"]}}}, "PLATFORMS_APP_CONFIG": {"Edith": {"slackChannel": "edith-staging-deployment"}, "Shield": {"slackChannel": "shield-staging-deployment", "dcLayerMap": {"US-VIRGINIA": ["application"]}}, "Blink": {"slackChannel": "blink-staging-deployment", "dcLayerMap": {"US-VIRGINIA": ["application"]}}, "Q-Lander": {"slackChannel": "qlander-staging-deployment"}, "SparrowPay": {"slackChannel": "sparrowpay-staging-deployment"}, "Attainment": {"slackChannel": "attainment-staging-deployment"}}, "CLOUDFLARE": {"ACCOUNT_ID": "6048a0aad235e934adb387c09b848e33", "WEBSITES": {"EdithDocs": {"slackChannel": "edith-staging-deployment", "staging": {"domain": "docs.salesparrow.com", "envs": {"SITE_DOMAIN": "https://edith-api.salesparrow.com", "API_URL": "https://edith-api.salesparrow.com"}, "projectName": "edith-docs-salesparrow"}, "TypeSparrow": {"domain": "docs.typesparrow.com", "envs": {"SITE_DOMAIN": "https://edith.typesparrow.com", "API_URL": "https://edith.typesparrow.com"}, "projectName": "edith-docs-<PERSON><PERSON><PERSON>"}}, "SSDocs": {"slackChannel": "staging-deployments", "staging": {"zoneId": "f19e64f2d66786c8cc2bff45c027b9a3", "domain": "developers.salesparrow.com", "envs": {"ALGOLIA_INDEX_NAME": "staging", "SURVEYSPARROW_DATA_CENTERS": "[{\\\"id\\\":\\\"1\\\",\\\"name\\\":\\\"United States\\\",\\\"value\\\":\\\"US\\\",\\\"url\\\":\\\"https://app.signsparrow.com\\\"},{\\\"id\\\":\\\"2\\\",\\\"name\\\":\\\"Europe\\\",\\\"value\\\":\\\"EU\\\",\\\"url\\\":\\\"https://app.salesparrow.com\\\"},{\\\"id\\\":\\\"3\\\",\\\"name\\\":\\\"Asia pacific\\\",\\\"value\\\":\\\"AP\\\",\\\"url\\\":\\\"http://app.salesparrow.com\\\"},{\\\"id\\\":\\\"4\\\",\\\"name\\\":\\\"Middle East\\\",\\\"value\\\":\\\"ME\\\",\\\"url\\\":\\\"http://app.salesparrow.com\\\"},{\\\"id\\\":\\\"5\\\",\\\"name\\\":\\\"United Kingdom\\\",\\\"value\\\":\\\"EU-LN\\\",\\\"url\\\":\\\"http://app.salesparrow.com\\\"},{\\\"id\\\":\\\"6\\\",\\\"name\\\":\\\"Sydney\\\",\\\"value\\\":\\\"AP-SY\\\",\\\"url\\\":\\\"http://app.salesparrow.com\\\"},{\\\"id\\\":\\\"7\\\",\\\"name\\\":\\\"Canada\\\",\\\"value\\\":\\\"CA\\\",\\\"url\\\":\\\"http://app.salesparrow.com\\\"}]", "SURVEYSPARROW_SWAGGER_URL": "https://api.salesparrow.com/swagger.json", "ALGOLIA_INDEX_KEY": "staging", "ALGOLIA_INDEX_WRITE_KEY": "********************************", "ALGOLIA_INDEX_READ_KEY": "********************************", "ALGOLIA_APP_ID": "GDXDICAFF8", "SURVEY_DEVELOPER_DOCS_URL": "https://developers.salesparrow.com"}, "projectName": "dev-docs-salesparrow"}}, "SparrowConnect": {"slackChannel": "staging-deployments", "staging": {"domain": "sc.salesparrow.com", "envs": {"REACT_APP_CACHE_DURATION": "3600000", "REACT_APP_HUB_BACKEND_HOSTNAME": "https://messages.typesparrow.com"}, "projectName": "sparrowconnect-salesparrow"}}}}, "ARGOCD": {"domain": "argocd.salesparrow.com"}, "SQUADS": ["Analyse", "AppNest", "Billing", "Build-Collect", "Deal", "Distribution", "EUI", "Growth", "GTMDataOps", "Hygiene", "Integrations", "Mobile", "ReputationManagement", "<PERSON><PERSON>", "TicketManagement"], "SQUAD_BRANCH_ENV_MAP": {"Billing-Helpsparrow": {"branch": "sse-squad-billing", "env": "helps<PERSON><PERSON>", "namespace": "helps<PERSON><PERSON>", "color": "#0000FF", "appName": "helpsparrow-squad-env-us-virginia"}, "Analyse-Noforms": {"branch": "sse-squad-analyse", "env": "noforms", "namespace": "noforms", "color": "#FFFF00", "appName": "noforms-squad-env-us-virginia"}, "Integrations-Servicesparrow": {"branch": "sse-squad-integrations", "env": "servicesparrow", "namespace": "servicesparrow", "color": "#FF00FF", "appName": "servicesparrow-squad-env-us-virginia"}, "AppNest-Signsparrow": {"branch": "sse-squad-appnest", "env": "signsparrow", "namespace": "signsparrow", "color": "#00FFFF", "appName": "signsparrow-squad-env-us-virginia"}, "Sre-S1-Performsparrow": {"branch": "sse-squad-sre", "env": "s1-<PERSON>parrow", "namespace": "s1-<PERSON>parrow", "color": "#FFA500", "appName": "s1-performsparrow-squad-env-us-virginia"}, "ReputationManagement-Officesparrow": {"branch": "sse-squad-reputation", "env": "officesparrow", "namespace": "officesparrow", "color": "#978CF9", "appName": "officesparrow-squad-env-us-virginia"}, "TicketManagement-Seosparrow": {"branch": "sse-squad-ticket", "env": "<PERSON><PERSON><PERSON><PERSON>", "namespace": "<PERSON><PERSON><PERSON><PERSON>", "color": "#008000", "appName": "seos<PERSON><PERSON>-squad-env-us-virginia"}, "Deal-Marketsparrow": {"branch": "sse-squad-deal", "env": "marketsparrow", "namespace": "marketsparrow", "color": "#000080", "appName": "marketsparrow-squad-env-us-virginia"}, "Build-Collect-Datasparrow": {"branch": "sse-squad-build-collect", "env": "datasparrow", "namespace": "datasparrow", "color": "#800000", "appName": "datasparrow-squad-env-us-virginia"}, "Distribution-Sparrowzen": {"branch": "sse-squad-distribution", "env": "sparrowzen", "namespace": "sparrowzen", "color": "#009507", "appName": "sparrowzen-squad-env-us-virginia"}, "Mobile-Surveytools": {"branch": "sse-squad-mobile", "env": "surveytools", "namespace": "surveytools", "color": "#008080", "appName": "surveytools-squad-env-us-virginia"}, "Hygiene-Typesparrow": {"branch": "sse-squad-cart-hygiene", "env": "typesparrow", "namespace": "typesparrow", "color": "#FF0080", "appName": "typesparrow-squad-env-us-virginia"}, "Growth-Helpsparrow": {"branch": "sse-squad-growth", "env": "helps<PERSON><PERSON>", "namespace": "helps<PERSON><PERSON>", "color": "#0000FF", "appName": "helpsparrow-squad-env-us-virginia"}, "GTMDataOps-Helpsparrow": {"branch": "sse-squad-gtmdataops", "env": "helps<PERSON><PERSON>", "namespace": "helps<PERSON><PERSON>", "color": "#0000FF", "appName": "helpsparrow-squad-env-us-virginia"}, "EUI-Talksparrow": {"branch": "sse-squad-eui", "env": "talksparrow", "namespace": "talksparrow", "color": "#070078", "appName": "talksparrow-squad-env-us-virginia"}, "CommonPool-Sparrowsuite": {"env": "sparrowsuite", "namespace": "sparrowsuite", "color": "#A52A2A", "appName": "sparrowsuite-squad-env-us-virginia"}, "CommonPool-Pagesparrow": {"env": "pagesparrow", "namespace": "pagesparrow", "color": "#FFD700", "appName": "pagesparrow-squad-env-us-virginia"}, "CommonPool-Automatesparrow": {"env": "automatesparrow", "namespace": "automatesparrow", "color": "#808000", "appName": "automatesparrow-squad-env-us-virginia"}, "CommonPool-Wingssparrow": {"env": "<PERSON><PERSON><PERSON>row", "namespace": "<PERSON><PERSON><PERSON>row", "color": "#8000FF", "appName": "wingssparrow-squad-env-us-virginia"}}, "MOBILE": {"FASTLANE": {"AndroidOffline": {"backendEnv": {"salesparrow": "salesparrowRelease", "pagesparrow": "pagesparrowRelease", "sparrowsuite": "sparrowsuiteRelease", "signsparrow": "signsparrowRelease", "marketsparrow": "marketsparrowRelease", "sparrowdesk": "sparrowdeskRelease", "automatesparrow": "automatesparrowRelease", "noforms": "noformsRelease", "datasparrow": "datasparrowRelease", "officesparrow": "officesparrowRelease", "servicesparrow": "servicesparrowRelease", "seosparrow": "seosparrowRelease", "helpsparrow": "helpsparrowRelease", "typesparrow": "typesparrowRelease", "talksparrow": "talksparrowRelease", "surveytools": "surveytoolsRelease", "sparrowzen": "sparrowzenRelease", "production": "release"}, "lanes": ["android alpha", "android skip_version_number", "android skip_build_number"]}}, "IOS_CODE_MAGIC": {"IOSOffline": {"backendScheme": ["SurveySparrow Prestaging Build", "SurveySparrow Sparrowsuite Build", "SurveySparrow Datasparrow Build", "SurveySparrow Officesparrow Build", "SurveySparrow Thrivesparrow Build", "SurveySparrow Marketsparrow Build", "SurveySparrow Pagesparrow Build", "SurveySparrow Servicesparrow Build", "SurveySparrow Signsparrow Build", "SurveySparrow Helpsparrow Build", "SurveySparrow Salesparrow Build", "SurveySparrow Seosparrow Build", "SurveySparrow Sparrowdesk Build", "SurveySparrow Sparrowzen Build", "SurveySparrow Typesparrow Build", "SurveySparrow Surveytools Build", "SurveySparrow Production Build"], "workflowIds": ["react-native-ios-staging", "react-native-ios-build-ipa"]}}}}