{"CONFIG_REPO": "*****************:surveysparrow/sparrowdesk-staging-config.git", "ARGOCD": {"userName": "admin", "domain": "argocd.campaignsparrow.com", "ACTIONS": {"deploy": "KUSTOMIZE_DEPLOY", "restart": "RESTART", "configUpdate": "CONFIG_UPDATE"}}, "ASSET_BUCKET": "chatbot-widget.campaignsparrow.com", "HELPDESK": {"appName": "helpdesk", "appRepo": "sd-app-backend.git", "backendDockerfilePath": "application/helpdesk-application/Dockerfiles/Staging.Dockerfile", "frontendDockerfilePath": "./DockerFiles/staging.dockerfile", "frontendEnvFile": "helpdeskFrontend.env", "versionPs": "/frontend/version", "versionKey": "VERSION", "deployments": ["helpdesk"], "configmap": "helpdesk-configmap", "migrationConfigmap": "helpdesk-migration-configmap", "runMigrations": true}, "CHATBOT": {"appName": "chatbot", "appRepo": "sd-app-backend.git", "backendDockerfilePath": "application/chatbot-application/Dockerfiles/Staging.Dockerfile", "frontendDockerfilePath": "./DockerFiles/chat.dockerfile", "frontendEnvFile": "chatbotFrontend.env", "migrationConfigmap": "chatbot-migration-configmap", "versionKey": "VERSION", "deployments": ["chatbot"], "configmap": "chatbot-configmap", "runMigrations": false}, "WEBSOCKET": {"appName": "websocket", "appRepo": "ws-server.git", "backendDockerfilePath": "Dockerfile", "deployments": ["websocket"], "configmap": "websocket-configmap", "runMigrations": false}, "HAPROXY": {"deployments": ["haproxy"], "configmap": "haproxy-config"}, "STAGING_ENVIRONMENTS": {"BERLIN": {"env": "berlin", "namespace": "berlin", "argocdAppName": "desk-berlin", "ecrApplicationRepo": "sd-berlin"}}, "BULLBOARD": {"argocdAppName": "desk-staging-bullboard", "configmap": "sparrowdesk-bullboard-configmap", "deployments": ["sparrowdesk-bullboard"]}, "ACCOUNT_CONFIG": {"crossAccount": true, "roleArn": "arn:aws:iam::************:role/CrossAccountJenkinsWorker", "accountId": "************"}, "CLOUDFLARE": {"pipelineName": "SparrowDesk/Website/CloudflareCachePurge", "baseDomain": "return ['www.campaignsparrow.com']", "DOMAINS": {"www.campaignsparrow.com": {"zoneId": "b6d5871b781e85c87a1eb09ee4e0c7dd"}}}, "ECR_REGISTRY": {"us-east-1": "************.dkr.ecr.us-east-1.amazonaws.com"}, "EKS": {"cluster": "sd-staging-cluster"}, "WEBSITE": {"websiteDeploymentsChannel": "sd-staging-deployments-status", "SparrowDesk/Website/CampaignSparrow/WebsiteDeployment": {"domain": "www.campaignsparrow.com", "argocdAppName": "sparrowdeskstaging-website-us-east-1", "ecrRepo": "sd-website", "secretName": "sd-website", "envFile": "website/staging.env", "kustomizePath": "sparrowdesk/website/kustomize/overlays/staging/us-east-1/sparrowdeskstaging/kustomization.yaml"}}}