{"CONFIG_REPO": "*****************:surveysparrow/sparrowdesk-production-config.git", "ARGOCD": {"userName": "admin", "domain": "argocd.campaignsparrow.com", "ACTIONS": {"deploy": "KUSTOMIZE_DEPLOY", "restart": "RESTART", "configUpdate": "CONFIG_UPDATE"}}, "ECR_REGISTRY": {"us-east-1": "************.dkr.ecr.us-east-1.amazonaws.com"}, "ACCOUNT_CONFIG": {"crossAccount": true, "roleArn": "arn:aws:iam::************:role/CrossAccountJenkinsWorker", "accountId": "************"}, "CLOUDFLARE": {"pipelineName": "SparrowDesk/Website/CloudflareCachePurge", "baseDomain": "return ['www.sparrowdesk.com']", "DOMAINS": {"www.sparrowdesk.com": {"zoneId": "46642b5f2b16ccafe61b925f9cc9a642"}}}, "WEBSITE": {"websiteDeploymentsChannel": "sparrowdesk-website-deployment-status", "SparrowDesk/Website/SparrowDesk/WebsiteDeployment": {"domain": "www.sparrowdesk.com", "argocdAppName": "sparrowdesk-website-us-east-1", "ecrRepo": "sd-website", "secretName": "sparrowdesk-website", "envFile": "website/production.env", "kustomizePath": "sparrowdesk/website/kustomize/overlays/production/us-east-1/sparrowdesk/kustomization.yaml"}}}