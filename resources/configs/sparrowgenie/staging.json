{"CONFIG_REPO": "*****************:surveysparrow/sparrowgenie-staging-config.git", "ASSET_BUCKET": "assets.geniestaging.com", "GENIE": {"appName": "genie", "appRepo": "sg-app-backend.git", "backendDockerfilePath": "Dockerfiles/Staging.Dockerfile", "frontendDockerfilePath": "./DockerFiles/staging.dockerfile", "frontendEnvFile": "frontend.env", "versionPs": "/frontend/version", "versionKey": "SPARROW_GENIE_BUNDLE_FINGERPRINT", "deployments": ["application"], "configmap": "genie-configmap", "migrationConfigmap": "genie-migration-configmap", "runMigrations": true}, "HAPROXY": {"deployments": ["haproxy"], "configmap": "haproxy-config"}, "STAGING_ENVIRONMENTS": {"BATMAN": {"env": "batman", "namespace": "batman", "argocdAppName": "genie-batman", "ecrApplicationRepo": "sg-batman"}}, "BULLBOARD": {"argocdAppName": "genie-staging-bullboard", "configmap": "sparrowgenie-bullboard-configmap", "deployments": ["sparrowgenie-bullboard"]}, "ACCOUNT_CONFIG": {"crossAccount": true, "roleArn": "arn:aws:iam::************:role/CrossAccountJenkinsWorker", "accountId": "************"}, "CLOUDFLARE": {"pipelineName": "SparrowGenie/Website/CloudflareCachePurge", "baseDomain": "return ['www.geniestaging.com']", "DOMAINS": {"www.geniestaging.com": {"zoneId": "2fbc74df554e57e1f213e6afa86f9ab3"}}}, "ECR_REGISTRY": {"us-east-1": "************.dkr.ecr.us-east-1.amazonaws.com"}, "EKS": {"cluster": "sparrowgenie-staging-cluster", "region": "us-east-1"}, "WEBSITE": {"websiteDeploymentsChannel": "sparrowgenie-website-deployment-status", "ARGOCD": {"username": "admin", "domain": "argocd.campaignsparrow.com", "ACTIONS": {"deploy": "KUSTOMIZE_DEPLOY", "restart": "RESTART", "configUpdate": "CONFIG_UPDATE"}}, "SparrowGenie/Website/GenieStaging/WebsiteDeployment": {"domain": "www.geniestaging.com", "argocdAppName": "geniestaging-website-us-east-1", "secretName": "sg_website", "ecrRepo": "sg-website", "envFile": "Website/geniestaging/staging.env", "kustomizePath": "sparrowgenie/website/kustomize/overlays/staging/us-east-1/geniestaging/kustomization.yaml"}}}