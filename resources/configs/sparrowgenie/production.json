{"CONFIG_REPO": "*****************:surveysparrow/sparrowgenie-production-config.git", "ACCOUNT_CONFIG": {"crossAccount": true, "roleArn": "arn:aws:iam::************:role/CrossAccountJenkinsWorker", "accountId": "************"}, "CLOUDFLARE": {"pipelineName": "SparrowGenie/Website/CloudflareCachePurge", "baseDomain": "return ['www.sparrowgenie.com', 'www.saasspice.com']", "DOMAINS": {"www.sparrowgenie.com": {"zoneId": "09bc5b2b7264b66132e67be7136565fa"}, "www.saasspice.com": {"zoneId": "bc66c4e69a28bb91c0e39f29f3672c3a"}}}, "ECR_REGISTRY": {"us-east-1": "************.dkr.ecr.us-east-1.amazonaws.com"}, "WEBSITE": {"ecrRepoUrl": "************.dkr.ecr.us-east-1.amazonaws.com", "websiteDeploymentsChannel": "sparrowgenie-website-deployment-status", "ARGOCD": {"username": "admin", "domain": "argocd.campaignsparrow.com", "ACTIONS": {"deploy": "KUSTOMIZE_DEPLOY", "restart": "RESTART", "configUpdate": "CONFIG_UPDATE"}}, "SparrowGenie/Website/SparrowGenie/WebsiteDeployment": {"repo": "*****************:surveysparrow/sparrowgenie-website.git", "domain": "www.sparrowgenie.com", "argocdAppName": "sparrowgenie-website-us-east-1", "ecrRepo": "sg-website", "secretName": "sparrowgenie_website", "envFile": "Website/sparrowgenie-website/production.env", "kustomizePath": "sparrowgenie/website/kustomize/overlays/production/us-east-1/sparrowgenie/kustomization.yaml"}, "SparrowGenie/Website/SaasSpice/WebsiteDeployment": {"repo": "*****************:surveysparrow/saasspice-website.git", "domain": "www.saasspice.com", "argocdAppName": "saasspice-website-us-east-1", "ecrRepo": "saasspice-website", "secretName": "saasspice_website", "envFile": "Website/saasspice-website/production.env", "kustomizePath": "sparrowgenie/website/kustomize/overlays/production/us-east-1/saasspice/kustomization.yaml"}}}