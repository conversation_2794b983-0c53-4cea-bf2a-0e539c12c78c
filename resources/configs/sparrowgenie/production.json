{"ACCOUNT_CONFIG": {"crossAccount": true, "roleArn": "arn:aws:iam::************:role/CrossAccountJenkinsWorker", "accountId": "************"}, "CLOUDFLARE": {"pipelineName": "SparrowGenie/Website/CloudflareCachePurge", "baseDomain": "return ['www.sparrowgenie.com']", "DOMAINS": {"www.sparrowgenie.com": {"zoneId": "09bc5b2b7264b66132e67be7136565fa"}}}, "WEBSITE": {"websiteRepo": "*****************:surveysparrow/sparrowgenie-website.git", "configRepo": "*****************:surveysparrow/sparrowgenie-production-config.git", "ecrRepoUrl": "************.dkr.ecr.us-east-1.amazonaws.com", "websiteDeploymentsChannel": "sparrowgenie-website-deployment-status", "ARGOCD": {"username": "admin", "domain": "argocd.campaignsparrow.com", "ACTIONS": {"deploy": "KUSTOMIZE_DEPLOY", "restart": "RESTART", "configUpdate": "CONFIG_UPDATE"}}, "SparrowGenie/Website/SparrowGenie/WebsiteDeployment": {"domain": "www.sparrowgenie.com", "argocdAppName": "sparrowgenie-website-us-east-1", "ecrRepo": "sg-website", "envFile": "Website/production.env", "kustomizePath": "sparrowgenie/website/kustomize/overlays/production/us-east-1/sparrowgenie/kustomization.yaml"}}}