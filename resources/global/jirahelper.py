import requests
import base64
import json
import os
import argparse

CREDENTIALS        = os.environ['JIRA_TOKEN']
PROJECT_KEY        = os.environ['PROJECT_KEY']
CLOUD_ID           = "5105c137-e612-4c03-9cb9-f113906a655c"
ACTIVATION_ID      = "bd28778f-cc9e-40b0-85f6-219a7abcd61b"
SQUAD_CUSTOM_FIELD = "customfield_10168"
APPLICATION        = os.environ.get('APPLICATION', "")
SLACK_URL          = "*******************************************************************************"  

headers = {
  'Authorization': 'Basic ' + CREDENTIALS
}

def get_specific_version(version_list, release_version):
  for version in version_list:
    if (version['name'] == release_version):
      return version['id'], version['released']
  message = f"No version ID found for {release_version}"
  exit(message)


def get_versions():
  get_version_url = f"https://surveysparrow.atlassian.net/rest/api/latest/project/{PROJECT_KEY}/versions"
  response = requests.request("GET", get_version_url, headers=headers)
  return response.json()

def get_issues_on_version(version_id):
  get_issues_url = f"https://surveysparrow.atlassian.net/rest/api/2/search?jql=fixVersion={version_id}&fields=key,summary,issuetype,customfield_10168"
  response = requests.request("GET", get_issues_url, headers=headers)
  return response.json()

def get_squad_name(custom_field_values):
  squads = []
  for field in custom_field_values:
    squads.append(field['value'])

  return ','.join(squads)

def filter_necessary_fields_from_issues(issues_list):
  issue_type_issue_dict = {}
  for issue in issues_list:
    if issue['fields']['issuetype']['name'] not in issue_type_issue_dict:
      issue_type_issue_dict[issue['fields']['issuetype']['name']] = []

    issue_type_issue_dict[issue['fields']['issuetype']['name']].append({
      'key': issue['key'],
      'link': f"https://surveysparrow.atlassian.net/browse/{issue['key']}",
      'summary': issue['fields']['summary'],
      'squad': get_squad_name(issue['fields'][SQUAD_CUSTOM_FIELD])
    })
  return issue_type_issue_dict

def get_version_ari(version_id):
  version_ari = "ari:cloud:jira:" + CLOUD_ID + ":version/activation/" + ACTIVATION_ID + "/" + version_id
  return version_ari

def fetch_related_work(version_id):
    try:
      graphql_url = "https://surveysparrow.atlassian.net/gateway/api/graphql"
      version_ari = get_version_ari(version_id)
      graphql_payload = {
          "query": "query getRelatedWork($id: ID!){jira{version(id: $id){...on JiraVersion {id,name,relatedWorkV2{edges{cursor,node {...on JiraVersionRelatedWorkGenericLink{relatedWorkId,category,title,url}}}}}}}}",
          "variables": {
              "id": version_ari
          }
      }
      headers['Content-Type'] = "application/json"
      headers['X-Experimentalapi'] = "ariGraph,boardCardMove,deleteCard,JiraJqlBuilder,SoftwareCardTypeTransitions,jira-releases-v0,createCustomFilter,updateCustomFilter,deleteCustomFilter,customFilters,PermissionScheme,JiraIssue,projectStyle,startSprintPrototype,AddIssuesToFixVersion,JiraVersionResult,JiraIssueConnectionJql,JiraFieldOptionSearching,JiraIssueFieldMutations,JiraIssueDevInfoDetails,JiraIssueDevSummaryCache,JiraVersionWarningConfig,JiraReleaseNotesConfiguration,JiraUpdateReleaseNotesConfiguration,ReleaseNotes,ReleaseNotesOptions,DeploymentsFeaturePrecondition,UpdateVersionWarningConfig,UpdateVersionName,UpdateVersionDescription,UpdateVersionStartDate,UpdateVersionReleaseDate,VersionsForProject,RelatedWork,SuggestedRelatedWorkCategories,setIssueMediaVisibility,toggleBoardFeature,virtual-agent-beta,JiraProject,RoadmapsMutation,RoadmapsQuery,JiraApplicationProperties,JiraIssueSearch,JiraFilter,featureGroups,setBoardEstimationType,softwareBoards,name,AddRelatedWorkToVersion,RemoveRelatedWorkFromVersion,admins,canAdministerBoard,jql,globalCardCreateAdditionalFields,GlobalTimeTrackingSettings,ReleaseNotesOptions,search-experience,MoveOrRemoveIssuesToFixVersion,compass-beta,JiraIssueSearchStatus,DevOpsSummarisedDeployments,DevOpsSummarisedEntities,JiraDevOps,devOps,DevOpsProvider,JiraDevOpsProviders"
      
      response = requests.request(
          "POST", graphql_url, headers=headers, json=graphql_payload)
      
      json_response = response.json()
      related_work = json_response['data']['jira']['version']['relatedWorkV2']['edges']
      
      related_work_message = ""
      for report in related_work:
          related_work_message += f"<{report['node']['url']}|{report['node']['title']}>" + ", "
      
      related_work_message = related_work_message[:-2]
    except Exception as e:
      print("Exception while fetching related work - ", e)
      return None
    return related_work_message

def construct_slack_message(issues_list, deployment_tag):

  message_to_post = f"{APPLICATION} Deployment tag - `{deployment_tag}`\n"

  issue_key_desc = filter_necessary_fields_from_issues(issues_list)
  issues_order = ['Story', 'Task', 'Sub-task', 'Defect']
  for issue_type in issues_order:
    if (issue_type in issue_key_desc):
      message_to_post += "*" + issue_type + "*\n"
      for issue in issue_key_desc[issue_type]:
        message_to_post += f"<{issue['link']}|{issue['key']}> {issue['summary']} ({issue['squad']})\n"
      message_to_post += "\n"
  return { "text" : message_to_post }

def send_slack_message(message):

  headers['Content-Type'] = 'application/json'
  response = requests.request("POST", SLACK_URL, headers=headers, data=json.dumps(message))
  # print(response.text)

def release_version_in_jira(version_id):
  release_version_url = f"https://surveysparrow.atlassian.net/rest/api/2/version/{version_id}"
  payload = { 'released': True }
  headers['Content-Type'] = 'application/json'
  response = requests.request("PUT", release_version_url, headers=headers, data=json.dumps(payload))
  # print(response.text)


def main():
    try:
      parser = argparse.ArgumentParser(description="Utility to fetch jira issue keys")
      parser.add_argument('-k',
                          '--keys',
                          help="Fetch Issue Keys",
                          action='store_true')
      parser.add_argument('-p',
                          '--postmessage',
                          help="Post Deployment message in slack",
                          action='store_true')
      parser.add_argument('-r',
                          '--releaseversion',
                          help="Release Version in Jira",
                          action='store_true')
      args = parser.parse_args()

      # print("[+] Fetching version for release branch - ", os.environ['RELEASE_BRANCH'])
      version_list = get_versions()
      release_version_id, released = get_specific_version(version_list, os.environ['RELEASE_BRANCH'])
      # print("[+] Release Version ID - ", release_version_id)
      issues_list = get_issues_on_version(release_version_id)
      if (args.keys):
        issues = []
        for issue in issues_list['issues']:
          issues.append(issue['key'])
        print(issues)

      if (args.postmessage):
        slack_message = construct_slack_message(issues_list['issues'], os.environ['DEPLOYMENT_TAG'])
        related_work_for_release = fetch_related_work(release_version_id)
        if (related_work_for_release):
          slack_message['text'] += related_work_for_release
        # print("[+] Posting Message - ", slack_message)

        if (not released):
          send_slack_message(slack_message)
      
      if (args.releaseversion):
        release_version_in_jira(release_version_id)

    except Exception as e:
      print(e)
      exit(1)

if (__name__ == "__main__"):
    main()
