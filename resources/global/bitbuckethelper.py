import requests
import json
import os
import argparse
import time

CREDENTIALS = os.environ['BB_TOKEN']
WORKSPACE   = os.environ['WORKSPACE']
BASE_URL    = f"https://api.bitbucket.org/2.0/repositories/{WORKSPACE}"

headers = {
	'Authorization': f"Basic {CREDENTIALS}"
}

def get_pr_diff(repository, prid):
  diff_files = {'old': [], 'new': []}
  while (True):
    diff_url = f"{BASE_URL}/{repository}/pullrequests/{prid}/diffstat"
    response = requests.request("GET", diff_url, headers=headers)
    if (response.status_code == 200):
      diff_response = response.json()
      for diff in diff_response['values']:
        diff_files['old'].append(diff['old']['path'] if diff['old'] is not None else "null") # Using string null not to cause issues with 
        diff_files['new'].append(diff['new']['path'] if diff['new'] is not None else "null") # JSON Formatting and structure
    else:
      print(f"[-] Diffstat returned non 200 status code - {response.json()}")
      exit(1)

    if (diff_response.get('next', None)):
      diff_url = diff_response['next']
    else:
      break
  return diff_files

def get_open_prs(repository, source_branch = None):
  url = f"{BASE_URL}/{repository}/pullrequests?q=state=\"OPEN\"+AND+destination.branch.name!=\"master\""
  if source_branch is not None:
    url = f"{url}+AND+source.branch.name=\"{source_branch}\""
  response = requests.request("GET", url, headers=headers)
  json_response = response.json()
  return json_response['values']

def wait_till_pr_is_merged(repository, prid):
  pr_merge_status = 'OPEN'
  while(pr_merge_status != 'MERGED'):
    url = f"{BASE_URL}/{repository}/pullrequests/{prid}"
    response = requests.request("GET", url, headers=headers)
    if (response.status_code == 200):
      pr_merge_status = response.json()['state']
      time.sleep(10)
    else:
      print(f"[-] Merge PR returned non 200 status code - {response.json()}")
      exit(1)

def merge_pr_by_id(repository, prid):
  url = f"{BASE_URL}/{repository}/pullrequests/{prid}/merge"
  response = requests.request("POST", url, headers=headers)
  if (response.status_code == 200):
    wait_till_pr_is_merged(repository, prid)
    return response.json()
  else:
    print(f"[-] Merge PR returned non 200 status code - {response.json()}")
    exit(1)

def get_target_branch(repository, source_branch):
  open_prs = get_open_prs(repository, source_branch)
  if len(open_prs) == 0:
    print(f"[-] NO OPEN PRS FOUND FOR BRANCH - {source_branch}")
    exit(1)
  else:
    recent_pr = open_prs[0]
    return recent_pr['destination']['branch']['name']

def get_pr_id(repository, source_branch):
  open_prs = get_open_prs(repository, source_branch)
  if (len(open_prs) == 0):
    return None
  else:
    return open_prs[0]['id']
  
def get_build_status(repository, prid):
  url = f"{BASE_URL}/{repository}/pullrequests/{prid}/statuses"
  response = requests.request("GET", url, headers=headers)
  if (response.status_code == 200):
    json_response = response.json()
    return json_response['values']
  else:
    print(f"[-] Merge PR returned non 200 status code - {response.json()}")
    exit(1)

def get_pr_data(repository, prid):
  url = f"{BASE_URL}/{repository}/pullrequests/{prid}"
  response = requests.request("GET", url, headers=headers)
  if (response.status_code == 200):
    json_response = response.json()
    return json_response
  else:
    print(f"[-] Merge PR returned non 200 status code - {response.json()}")
    exit(1)

def build_status_update(key, name, status, description, current_build_url, repo_name, commit_id):
    url = f"{BASE_URL}/{repo_name}/commit/{commit_id}/statuses/build"
    data = {
        "key": key,
        "name": name,
        "state": status,
        "description": description,
        "url": current_build_url
    }
    response = requests.post(url, headers=headers, json=data)
    if response.status_code in [200, 201]:
        return response.json()
    else:
        print(f"Failed to update status. Response code: {response.status_code}, Response: {response.text}")
        exit(1)

def handle_output(output_to_print):
  if output_to_print is not None:
    if ((type (output_to_print) == list) or type (output_to_print) == dict):
      print(json.dumps(output_to_print))
    else:
      print(output_to_print)
  exit(0)

def print_file(repository:str, reference:str,  filepath:str):
  url = f"{BASE_URL}/{repository}/src/{reference}/{filepath}"
  response = requests.request("GET", url, headers=headers)
  if (response.status_code == 200):
    handle_output(response.text)
  else:
    print(f"[-] request returned non 200 status code - {response.json()}")
    exit(1)

def main():
  try:
    parser = argparse.ArgumentParser(description="Utility to interact with bitbucket")
    parser.add_argument('-r', '--repository', required=True, help="The repository for which we need to fetch data")
    subparsers = parser.add_subparsers(dest="operation", help="Available operations")
    subparsers.required = True
    parser_targetbranch = subparsers.add_parser("get-targetbranch", help="Get the target branch for a given source branch.")
    parser_targetbranch.add_argument("-s", "--sourcebranch", required=True, help="The source branch to get the target branch for.")
    parser_prids = subparsers.add_parser("get-prid", help="Get PR IDs for a given source branch.")
    parser_prids.add_argument("-s", "--sourcebranch", required=True, help="The source branch to get PR IDs for.")
    parser_mergeprs = subparsers.add_parser("merge-prs", help="Merge PRs for a given list of PR IDs.")
    merge_group = parser_mergeprs.add_mutually_exclusive_group(required=True)
    merge_group.add_argument("-p", "--prid", type=int, help="The PR IDs to merge (space-separated).")
    merge_group.add_argument("-s", "--sourcebranch", help="The source branch of the PR")
    parser_diffstat = subparsers.add_parser("get-diff", help="Get the diff from a given PR")
    parser_diffstat.add_argument("-p", "--prid", type=int, help="The PR ID to get the diff")
    parser_buildstatus = subparsers.add_parser("get-buildstatus", help="Get the build status from a given PR")
    parser_buildstatus.add_argument("-p", "--prid", type=int, help="The PR ID to get the build status")
    parser_prdata = subparsers.add_parser("get-prdata", help="Get the PR data from a given PR")
    parser_prdata.add_argument("-p", "--prid", type=int, help="The PR ID to get the PR data")
    parser_print_file = subparsers.add_parser("print-file", help="Print a file from the repository")
    parser_print_file.add_argument("-f", "--filepath", required=True, help="The file path to print from the repository")
    parser_print_file.add_argument("-r", "--reference", required=True, help="The source reference id to get the target file.")
    parser_buildstatusupdate = subparsers.add_parser("build-status-update", help="Update the build status for a given commit.")
    parser_buildstatusupdate.add_argument("-k", "--key", required=True, help="The key for the build status.")
    parser_buildstatusupdate.add_argument("-n", "--name", required=True, help="The name of the build.")
    parser_buildstatusupdate.add_argument("-s", "--status", required=True, help="The status of the build (e.g., SUCCESSFUL, FAILED).")
    parser_buildstatusupdate.add_argument("-d", "--description", required=True, help="A description of the build status.")
    parser_buildstatusupdate.add_argument("-u", "--url", required=True, help="The URL of the current build.")
    parser_buildstatusupdate.add_argument("-c", "--commit", required=True, help="The commit ID to update the status for.")

    args = parser.parse_args()

    if (args.operation == 'get-targetbranch'):
      target_branch = get_target_branch(args.repository, args.sourcebranch)
      handle_output(target_branch)

    if (args.operation == 'get-prid'):
      prid = get_pr_id(args.repository, args.sourcebranch)
      handle_output(prid)

    if (args.operation == 'get-diff'):
      diff = get_pr_diff(args.repository, args.prid)
      handle_output(diff)
    
    if (args.operation == 'merge-prs'):
      merge_pr_by_id(args.repository, args.prid)
    
    if (args.operation == 'get-buildstatus'):
      build_status = get_build_status(args.repository, args.prid)
      handle_output(build_status)
      
    if (args.operation == 'get-prdata'):
      pr_data = get_pr_data(args.repository, args.prid)
      handle_output(pr_data)
    
    if (args.operation == 'build-status-update'):
      build_status_update(args.key, args.name, args.status, args.description, args.url, args.repository, args.commit)

    if (args.operation == 'print-file'):
      print_file(args.repository, args.reference, args.filepath)

  except Exception as e:
    print(e)
    exit(1)

if __name__ == '__main__':
  main()