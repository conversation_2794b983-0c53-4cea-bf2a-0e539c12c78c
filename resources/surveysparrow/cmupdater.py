import yaml
import argparse
import os

def main():
	try:
		parser = argparse.ArgumentParser(description="Utility to update the configmap yaml")
		parser.add_argument('-c', '--config', help="Config map name", type=str)
		parser.add_argument('-n', '--namespace', help="Namespace for the configmap", type=str)
		parser.add_argument('-d', '--directory', help="Directory in which the config files reside", type=str)
		parser.add_argument('-e', '--exclude', help="Files to exclude from configmap", type=str)
		args = parser.parse_args()

		configmap_yaml = {}
		configmap_yaml['apiVersion'] = 'v1'
		configmap_yaml['kind'] = 'ConfigMap'
		configmap_yaml['metadata'] = {}
		configmap_yaml['metadata']['name'] = args.config
		configmap_yaml['metadata']['namespace'] = args.namespace
		configmap_yaml['data'] = {}

		files = os.listdir(args.directory)
		if args.exclude is not None:
			exclude_files = args.exclude.split(',')
			for exclude_file in exclude_files:
				print(exclude_file)
				if (exclude_file in files):
					files.remove(exclude_file)

		for file in files:
			file_path = os.path.join(args.directory, file)
			if file.endswith('.env'):
				# Special handling for .env files - parse each line as key-value
				with open(file_path, 'r') as f:
					for line in f:
						line = line.strip()
						if line and not line.startswith('#'):
							try:
								key, value = line.split('=', 1)
								configmap_yaml['data'][key.strip()] = value.strip()
							except ValueError:
								# Skip lines that don't have a '=' separator
								continue
			else:
				# Regular handling for other files
				with open(file_path, 'r') as f:
					configmap_yaml['data'][file] = f.read()

		yaml.dump(configmap_yaml, open('cm-template.yaml', 'w'))
	
	except Exception as e:
		print(f"==========> [ABORTING_BUILD] REASON : Exception catched - {e}. <==========")

if (__name__ == "__main__"):
  main()