package org.sparrowgenie

class CommonConstants {
  static final Map<String, Object> DEFAULTS = [
    "BITBUCKET_CREDENTIAL_ID": "BitBucketPrivateKey",
    "BITBUCKET_WORKSPACE": "surveysparrow",
    "DEPLOYMENT_PARAMETERS": [ "deployment", "service", "version", "image", "app", "configmap" ],
    "DEFAULT_JIRA_PROJECT_KEY": "SSE",
    "SLACK_TOKEN_ID": "SlackIntegrationToken",
    "SLACK_BOT_TOKEN_ID": "SlackBotToken",
    "STATUS_EMOJI_MAP": [
      "STARTED": ":rocket:",
      "FAILURE": ":bomb:",
      "SUCCESS": ":pirate_flag:",
      "FAILED": ":bomb:",
      "ABORTED": ":dizzy_face:",
      "INFO": ":scroll:",
      "APPROVAL": ":hourglass:",
      "MIGRATION_SUCCESS": ":pirate_flag:",
      "MIGRATION_FAILURE": ":bomb:"
    ],
    "STATUS_COLOR_MAP": [
      "SUCCESS": "#44FF44",
      "FAILURE": "#FF4444",
      "FAILED": "#FF4444",
      "ABORTED": "#FFA500",
      "INFO": "#FFAA66",
      "STARTED": "#4444FF",
      "APPROVAL": "#FFAA66",
      "MIGRATION_SUCCESS": "#44FF44",
      "MIGRATION_FAILURE": "#FF4444"
    ],
    "DEFAULT_REGION": "us-east-1",
    "DEFAULT_BRANCH": "master",
    "DEFAULT_DEPLOYMENTS_CHANNEL": "#sparrowgenie-staging-deployments",
    "ARTIFACTS_BUCKET": "sg-node-modules-staging",
    "ARGOCD": [
      "userName": "admin",
      "domain": "argocd.campaignsparrow.com",
      "ACTIONS": [
        "deploy": "KUSTOMIZE_DEPLOY",
        "restart": "RESTART",
        "configUpdate": "CONFIG_UPDATE"
      ]
    ],
    "MIGRATION": [
      "preJob": "pre-migration",
      "preFile": "pre-migration",
      "postJob": "post-migration",
      "postFile": "post-migration"
    ],
    "DEPLOYMENT": [
      "applicationPatchFile"  : "application.yaml",
      "stagingOverlayEksPath": "staging-eks/sparrowgenie/application/kustomize/overlays/staging",
      "stagingBaseEksPath"   : "staging-eks/sparrowgenie/application/kustomize/base"
    ],
    "GIT": [
      "websiteRepo": "*****************:surveysparrow/sparrowgenie-website.git",
      "frontendUrl": "*****************:surveysparrow/sg-app-frontend.git",
      "backendUrl": "*****************:surveysparrow/sg-app-backend.git",
      "gitProject": "*****************:surveysparrow"
    ]
  ]
}
