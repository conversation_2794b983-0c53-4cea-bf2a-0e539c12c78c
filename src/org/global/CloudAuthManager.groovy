package org.global

import org.global.CommonConstants

/**
 * CloudAuthManager - contains all the methods to communicate with CloudProviders based on region.
 */
class CloudAuthManager {

  /**
   * A reference to the script context, used to execute shell commands.
   */
  def script;

  /**
   * Region to which to authenticate to the cloud provider.
   */
  def region;

  /**
   * Cloud Config - incase of cross account authentication.
   */
  def accountConfig = [:];

  /**
   * Constructs a CloudAuthManager object with the provided script context.
   * 
   * @param script The script context to execute shell commands.
   * @param region the region to which to authenticate to.
   * @param accountConfig Hashmap Optional parameter - [ crossAccount: true, roleArn: 'AWSRoleArn', accountId: 'AWS Account ID', duration: 'Duration of the session'(By default 900 seconds) ] InCase of AWS, [ secretFileId: 'JenkinsCredentialID', projectId: 'GCP ProjectId' ] InCase of GCP
   */
  CloudAuthManager(script, region, accountConfig = [:]) {
    this.script = script
    this.region = region
    this.accountConfig = accountConfig
  }

  /**
   * Authenticates to AWS Cloud provider, can use roles if cross account auth is needed.
   * 
   * @param config Optional parameter - a hashmap crossAccount: true, roleArn: 'awsArn', region: 'awsRegion', 'accountId': 'aws account Id' are mandatory parameters
   * @return Environment variables with necessary keys in case of cross account else nothing.
   */
  void authenticateToAWS() {
    if (this.accountConfig.crossAccount) {
      if (!this.accountConfig.roleArn && !this.accountConfig.accountId) {
        script.error('Necessary parameters not provided - roleArn, accountId are mandatory paramters')
      }

      script.withAWS(region: this.region, role: this.accountConfig.roleArn, roleAccount: this.accountConfig.accountId, duration: this.accountConfig.duration ? this.accountConfig.duration : 900) {
        script.env.AWS_ACCESS_KEY_ID     = script.sh(script: "echo \$AWS_ACCESS_KEY_ID", returnStdout: true).trim()
        script.env.AWS_SECRET_ACCESS_KEY = script.sh(script: "echo \$AWS_SECRET_ACCESS_KEY", returnStdout: true).trim()
        script.env.AWS_SESSION_TOKEN     = script.sh(script: "echo \$AWS_SESSION_TOKEN", returnStdout: true).trim()
      }
    } else {
      script.echo(message: 'Using Instance role since it is the same account')
    }

    script.echo(message: 'Authentication to AWS successful')
  }

  /**
   * Authenticates to GCP Cloud provider.
   */
  void authenticateToGCP() {
    if (!config.secretFileId || !config.projectId) {
      script.error("GCP Secret File ID or Project ID not provided")
    }

    script.withCredentials([script.file(credentialsId: config.secretFileId, variable: 'GCP_KEY_FILE')]) {
      script.sh """
        gcloud auth activate-service-account --key-file=$GCP_KEY_FILE
        gcloud config set project ${config.projectId}
      """
      script.env.GOOGLE_APPLICATION_CREDENTIALS = script.env.GCP_KEY_FILE
      script.echo "Authenticated to GCP for project: ${config.projectId}"
    }
  }

  /**
   * Authenticate to Cloud Provider based on the map in common constants.
   *
   * @return cloudProvider as string
   */
  String authenticateToCloudProvider() {
    def cloudProvider = CommonConstants.DEFAULTS.REGION_CLOUD_MAP[this.region].toUpperCase()
    switch (cloudProvider) {
      case 'AWS':
        authenticateToAWS()
        break
      case 'GCP':
        authenticateToGCP()
        break
      default:
        script.error("Unsupported region - ${this.region}")
    }

    return cloudProvider;
  }

  /**
  * Clear the set credentials so you can proceed with using the Instance role.
  */
  void clearCredentials() {
    def cloudProvider = CommonConstants.DEFAULTS.REGION_CLOUD_MAP[this.region].toUpperCase()
    if (cloudProvider == 'AWS') {
      script.env.AWS_ACCESS_KEY_ID     = ''
      script.env.AWS_SECRET_ACCESS_KEY = ''
      script.env.AWS_SESSION_TOKEN     = ''
    }
  }

  /**
   * Authenticate to container registry based on cloud provider - ECR or Artifact Registry
   * 
   * @param registry (String) the registry url to authenticate to in the Cloud Provider (ECR in AWS, Artifact Registry in GCP)
   */
  void authenticateToContainerRegistry(registry) {
    def cloudProvider = authenticateToCloudProvider()
    switch (cloudProvider) {
      case 'AWS':
        script.sh(
          script: "aws ecr get-login-password --region ${this.region} | docker login --username AWS --password-stdin ${registry}"
        )
        break
      case 'GCP':
        script.sh(
          script: "gcloud auth configure-docker ${registry}"
        )
        break
      default:
        script.error("Unsupported region - ${this.region}")
    } 
  }

  /**
   * Authenticate to Kubernetes Cluster based on Cloud Provider - EKS or GCP
   * 
   * @param cluster (String) the clusterName to authenticate to in the Cloud Provider (EKS in AWS, GKE in GCP)
   */
  void authenticateToK8sCluster(cluster) {
    def cloudProvider = authenticateToCloudProvider()
    script.env.KUBECONFIG = "${script.env.WORKSPACE}/.${cluster}-config"
    switch (cloudProvider) {
      case 'AWS':
        script.sh(
          script: "aws eks --region ${this.region} update-kubeconfig --name ${cluster}"
        )
        break
      case 'GCP':
        script.sh(
          script: "gcloud container clusters get-credentials ${cluster} --region ${this.region}"
        )
        break
      default:
        script.error("Unsupported region - ${this.region}")
    }
  }
}
