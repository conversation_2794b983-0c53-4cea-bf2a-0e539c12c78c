package org.global

import groovy.json.JsonOutput
import groovy.json.JsonSlurper
import java.net.HttpURLConnection
import java.net.URL

/*
 * A utility for HTTP Communication. Making HTTP Requests instead of using cUrl from the CLI.
 */
class HttpUtils {
    /*
     * Makes the HTTP Request based on the supplied parameters.
     *
     * @param url (String) The url to which the HTTP call should be made.
     * @param method (String) The HTTP method to use. (GET, PUT, POST, PUT, DELETE, HEAD)
     * @param headers (Map) OPTIONAL The Headers to pass to the HTTP Request
     * @param body (Any) OPTIONAL The request body incase of requests which need body (eg.POST)
     * @param timeout (Int) OPTIONAL The HTTP Request timeout. (Default: 30000)
     * @return response (Map) A map with statusCode, headers and body
     */
    static Map request(String url, String method = 'GET', Map headers = [:], def body = null, int timeout = 30000) {
        def connection = new URL(url).openConnection() as HttpURLConnection
        connection.requestMethod = method.toUpperCase()
        connection.connectTimeout = timeout
        connection.readTimeout = timeout

        headers.each { key, value ->
            connection.setRequestProperty(key, value)
        }

        if (body && method != 'HEAD') {
            connection.doOutput = true
            def output = body instanceof String ? body : JsonOutput.toJson(body)
            connection.outputStream.write(output.bytes)
        }

        def responseCode = connection.responseCode
        def responseContent = ''

        if (method == 'HEAD') {
            // HEAD requests should only read the headers; no body
            responseContent = null
        } else if (responseCode < 400) {
            responseContent = connection.inputStream.text
        } else {
            responseContent = connection.errorStream?.text ?: ''
        }

        // Attempt to parse the response as JSON (if applicable)
        def parsedBody
        if (responseContent) {
            try {
                parsedBody = new JsonSlurper().parseText(responseContent)
            } catch (Exception e) {
                parsedBody = responseContent // Fallback to raw text
            }
        }

        return [
            statusCode: responseCode,
            headers   : connection.headerFields,
            body      : parsedBody
        ]
    }

    /*
     * Perform a GET Request
     *
     * @param url (String) The url to which the HTTP call should be made.
     * @param headers (Map) OPTIONAL The Headers to pass to the HTTP Request
     * @param timeout (Int) OPTIONAL The HTTP Request timeout. (Default: 30000)
     * @return response (Map) A map with statusCode, headers and body
     */
    static Map get(String url, Map headers = [:], int timeout = 30000) {
        request(url, 'GET', headers, null, timeout)
    }

    /*
     * Perform a POST Request
     *
     * @param url (String) The url to which the HTTP call should be made.
     * @param headers (Map) OPTIONAL The Headers to pass to the HTTP Request
     * @param body (Any) OPTIONAL The request body incase of requests which need body (eg.POST)
     * @param timeout (Int) OPTIONAL The HTTP Request timeout. (Default: 30000)
     * @return response (Map) A map with statusCode, headers and body
     */
    static Map post(String url, Map headers = [:], def body = null, int timeout = 30000) {
        request(url, 'POST', headers, body, timeout)
    }

    /*
     * Perform a PUT Request
     *
     * @param url (String) The url to which the HTTP call should be made.
     * @param headers (Map) OPTIONAL The Headers to pass to the HTTP Request
     * @param body (Any) OPTIONAL The request body incase of requests which need body (eg.POST)
     * @param timeout (Int) OPTIONAL The HTTP Request timeout. (Default: 30000)
     * @return response (Map) A map with statusCode, headers and body
     */
    static Map put(String url, Map headers = [:], def body = null, int timeout = 30000) {
        request(url, 'PUT', headers, body, timeout)
    }

    /*
     * Perform a DELETE Request
     *
     * @param url (String) The url to which the HTTP call should be made.
     * @param headers (Map) OPTIONAL The Headers to pass to the HTTP Request
     * @param body (Any) OPTIONAL The request body incase of requests which need body (eg.POST)
     * @param timeout (Int) OPTIONAL The HTTP Request timeout. (Default: 30000)
     * @return response (Map) A map with statusCode, headers and body
     */
    static Map delete(String url, Map headers = [:], def body = null, int timeout = 30000) {
        request(url, 'DELETE', headers, body, timeout)
    }

    /*
     * Perform a HEAD Request
     *
     * @param url (String) The url to which the HTTP call should be made.
     * @param headers (Map) OPTIONAL The Headers to pass to the HTTP Request
     * @param timeout (Int) OPTIONAL The HTTP Request timeout. (Default: 30000)
     * @return response (Map) A map with statusCode, headers and body
     */
    static Map head(String url, Map headers = [:], int timeout = 30000) {
        request(url, 'HEAD', headers, null, timeout)
    }
}

