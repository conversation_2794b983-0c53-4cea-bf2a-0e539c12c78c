package org.global

/**
 * Utility class for handling release tasks
 */
class ReleaseUtils {
  /**
   * The <PERSON> script object
   */
  def script;

  /**
   * Constructor for the ReleaseUtils class
   * @param script The Jenkins script object
   */
  ReleaseUtils(script) {
    this.script = script;
  }

  /**
   * Get the bump type for the release
   * @return (String) The bump type
   */
  String getBumpType() {
    script.sh (script:
      """
        git fetch --tags
        npm install conventional-recommended-bump conventional-changelog-angular
        npx conventional-recommended-bump -p angular
      """
    )

    def bumpType = script.sh (script: "npx conventional-recommended-bump -p angular", returnStdout: true).trim()
    return bumpType
  }

  /**
   * Bump the version for the release
   * @param version (String) The current version
   * @param bumpType (String) The bump type
   * @return (String) The new version
   */
  String bumpVersion(String version, String bumpType) {
    def versionString = version.startsWith('v') ? version.substring(1) : version
    def (major, minor, patch) = versionString.tokenize('.').collect { it.toInteger() }

    def newVersion = ""
    switch (bumpType) {
      case "major":
        newVersion = "${major + 1}.0.0"
        break
      case "minor":
        newVersion = "${major}.${minor + 1}.0"
        break
      case "patch":
        newVersion = "${major}.${minor}.${patch + 1}"
        break	
      default:
        newVersion = "${major}.${minor}.${patch + 1}"
        break
    }

    return version.startsWith('v') ? "v${newVersion}" : newVersion
  }

  /**
   * Write the changelog for the release
   * @param version (String) The current version
   */
  void writeChangelog(String version) {
    script.sh (script: 
      """
        git fetch --tags
        npm install conventional-changelog-cli conventional-changelog-angular
        npx conventional-changelog -p angular -i CHANGELOG.md -s ${version.startsWith('v') ? '--tag-prefix=v' : ''} -u --commit-path=. -r0
      """
    )
  }
}
