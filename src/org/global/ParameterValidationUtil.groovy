package org.global

/**
 * Validate the valid parameters and mandatory parameters of any method
 */
class ParameterValidationUtil {

  /**
   *  Method to validate parameters from the parameter map
   *
   * @param validParams (List<String>) valid parameters for a given method
   * @param suppliedParams (Map) the parameters supplied to the method or pipeline step
   * @param mandatoryParams (List<String>) Optional, Mandatory parameters for the give method or pipeline step
   */
  static void validateParameterMap(List<String> validParams = [], Map suppliedParams = [:], List<String> mandatoryParams = []) {
    suppliedParams.keySet().findAll { !(it in validParams) }.each {
      throw new IllegalArgumentException("Unsupported option key: '${it}'")
    }

    mandatoryParams.findAll { !(it in suppliedParams.keySet()) }.each {
      throw new IllegalArgumentException("Missing mandatory parameter: '${it}'")
    }
  }
}