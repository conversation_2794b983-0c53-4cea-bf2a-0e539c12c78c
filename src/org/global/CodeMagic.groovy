package org.global

import groovy.json.JsonOutput

/**
 * A utility to interact with the CodeMagic API.
 * This provides methods to trigger builds, monitor deployments, fetch logs, and download artifacts
 * through the CodeMagic REST API.
 */
class CodeMagic {

  /**
   * Reference to the script context, used to execute shell commands.
   */
  def script

  /**
   * Counter for build stages.
   */
  private stageCount = 0

  /**
   * The CodeMagic application ID.
   */
  def codeMagicAppId

  /**
   * The CodeMagic workflow ID.
   */
  def workflowId

  /**
   * The CodeMagic API token.
   */
  private def codeMagicToken

  /**
   * Build ID of the CodeMagic deployment.
   */
  def buildId

  /**
   * Constructs a CodeMagic object with the provided script context.
   *
   * @param script (reference) The script context to execute shell commands.
   * @param codeMagicAppId (String) The CodeMagic application ID.
   * @param workflowId (String) The CodeMagic workflow ID.
   * @param codeMagicToken (String) The CodeMagic API token.
   */
  CodeMagic(script, String codeMagicAppId, String workflowId, String codeMagicToken) {
    this.script = script
    this.codeMagicAppId = codeMagicAppId
    this.workflowId = workflowId
    this.codeMagicToken = codeMagicToken
  }

  /**
   * Triggers a CodeMagic deployment using the provided parameters.
   *
   * @param buildParams (Map) The build parameters payload.
   * - branch (String): The branch to build.
   * - workflowId (String): The CodeMagic workflow ID.
   * - appId (String): The CodeMagic application ID.
   * - environment (Map): The environment variables to use for the build.
   *   - variables (Map): The environment variables to use for the build.
   *     - VAR_NAME (String): The value of the variable.
   * - labels (List): The labels to apply to the build.
   * @return (Map) A map containing the build ID and response code.
   */
  Map triggerCodeMagicDeployment(Map buildParams) {
    def json = JsonOutput.toJson(buildParams)
    def responseCode = script.sh(script: """curl -s -w "%{http_code}" -o response.json \\
      -H "Authorization: Bearer ${this.codeMagicToken}" \\
      -H "Content-Type: application/json" \\
      -d '${JsonOutput.prettyPrint(json)}' \\
      https://api.codemagic.io/builds""", returnStdout: true).trim().toInteger()

    def responseBody = script.readJSON file: 'response.json'
    this.buildId = responseBody.buildId
    return ['buildId': this.buildId, 'responseCode': responseCode]
  }

  /**
   * Prints CodeMagic logs for completed build stages.
   *
   * @param buildData (Map) The build data returned from CodeMagic API.
   */
  void printCompletedBuildStageLogs(Map buildData) {
    for (def stage = stageCount; stage < buildData.build.buildActions.size(); stage++) {
      def stageData = buildData.build.buildActions[stage]
      def stageName = stageData.name
      def stageStatus = stageData.status
      if (!['success', 'failed'].contains(stageStatus)) {
        break
      }
      stageCount++
      this.printStageLogs(stageData, stageName, stageStatus)
    }
  }

  /**
   * Prints logs for a specific build stage or subaction.
   *
   * @param data (Map) The stage or subaction data. The data is a map with the following keys:
   * - logUrl (String): The URL to the logs.
   * - subactions (List): A list of subactions. Each subaction is a map with the following keys: (optional)
   *   - command (String): The command to run.
   *   - status (String): The status of the subaction.
   * @param name (String) The name of the stage or subaction.
   * @param status (String) The status of the stage or subaction.
   */
  private void printStageLogs(Map data, String name, String status) {
    def logsUrl = data.logUrl
    if (!(logsUrl instanceof net.sf.json.JSONNull)) {
      def logResponseCode = script.sh(script: """curl -s -w "%{http_code}" -o logResponse.txt \\
        -H "Authorization: Bearer ${this.codeMagicToken}" \\
        -H "Content-Type: application/json" \\
        ${logsUrl}""", returnStdout: true).trim().toInteger()

      def logResponse = script.readFile file: 'logResponse.txt'

      if (logResponseCode != 200) {
        script.error("Failed to fetch CodeMagic logs for ${name}: ${logResponse}")
      }

      script.sh "echo \"CodeMagic logs for ${name} [${status}]:\" | tee -a CodeMagicLogs.txt"
      script.sh "cat logResponse.txt | tee -a CodeMagicLogs.txt"
    }

    if (data.subactions) {
      for (def subAction in data.subactions) {
        this.printStageLogs(subAction, subAction.command, subAction.status)
      }
    }
  }

  /**
   * Gets the deployment status for a given build ID.
   *
   * @param buildId (String) The build ID to check.
   * @return (Map) A map containing the status code and build data.
   */
  Map<String, Object> getDeploymentStatus(String buildId = this.buildId) {
    def responseCode = script.sh(script: """curl -s -w "%{http_code}" -o statusResponse.json \\
      -H "Authorization: Bearer ${this.codeMagicToken}" \\
      -H "Content-Type: application/json" \\
      https://api.codemagic.io/builds/${buildId}""", returnStdout: true).trim().toInteger()
    def buildData = script.readJSON file: 'statusResponse.json'
    return ['responseCode': responseCode, 'body': buildData]
  }

  /**
   * Cancels a running CodeMagic deployment.
   *
   * @param buildId (String) The build ID to cancel.
   */
  void cancelCodeMagicDeployment(String buildId = this.buildId) {
    def responseCode = script.sh(script: """curl -w '%{http_code}' -X POST \\
      -H 'Authorization: Bearer ${this.codeMagicToken}' \\
      -H 'Content-Type: application/json' \\
      --output cancel_response.json \\
      https://api.codemagic.io/builds/${buildId}/cancel""", returnStdout: true).trim().toInteger()

    if (responseCode != 200 && responseCode != 208) {
      def content = script.readFile file: 'cancel_response.json'
      script.error("Failed to cancel CodeMagic deployment:\n ${content}")
    }
  }

  /**
   * Gets the download link for the IPA artifact from a build.
   *
   * @param buildId (String) The build ID.
   * @return (String) The URL of the IPA artifact, or null if not found.
   */
  String getArtifactDownloadUrl(String buildId = this.buildId) {
    def response = this.getDeploymentStatus(buildId)
    def buildData = response.body

    def artifacts = buildData.build.artefacts
    def artifactUrl = artifacts.find { it.type == 'ipa' }?.url

    def redirectedArtifactUrl = script.sh(script: """curl -s -w "%{redirect_url}" \\
      -H "Authorization: Bearer ${this.codeMagicToken}" \\
      -H "Content-Type: application/json" --output /dev/null \\
      ${artifactUrl}""", returnStdout: true).trim()

    return redirectedArtifactUrl
  }

}
