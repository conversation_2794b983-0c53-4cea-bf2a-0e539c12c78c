package org.global

/**
 * Get the messages based on the state of the deployment
 * This is a temporary class, needs to be replaced and or deleted.
 */
class MessageUtils {

  /**
   * Get the message based on the deployment progress
   *
   * @param messageType (String) the state of the deployment - valid values - 'DEPLOYMENT_STARTED', 'DE<PERSON>OYMENT_SUCCESSFUL', '<PERSON>PLOYMENT_FAILED', 'DEPLOYMENT_ABORTED'
   * @param application (String) the application name to add in the message
   * @param dc (String) the datacenter in which the deployment is going on
   * @return message (String) the formatted message
   */
  static String getDeploymentProgressMessage(String messageType, String application, String dc) {
    def message = ""
    switch (messageType) {
      case 'DEPLOYMENT_STARTED':
        message = "${application} Deployment started in ${dc}"
        break
      case 'DEPLOYMENT_SUCCESSFUL':
        message = "${application} Deployment successful in ${dc}"
        break
      case 'DEPLOYMENT_FAILED':
        message = "${application} Deployment failed in ${dc}"
        break
      case 'DEPLOYMENT_ABORTED':
        message = "${application} Deployment aborted in ${dc}"
        break
      default:
        message = "${application} Deployment in progress in ${dc}"
        break
    }

    return message
  }
}