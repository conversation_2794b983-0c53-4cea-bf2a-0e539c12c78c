package org.global

/**
 * A utility to interact with ArgoCD.
 * This provides methods to interact with the ArgoCD installation on a
 * Kubernetes cluster through CLI Commands.
 */
class ArgoCd {

  /**
   * A reference to the script context, used to execute shell commands.
   */
  def script;

  /**
   *  ArgoCD appname
   */
  def appName;

  /**
   * ArgoCD domain
   */
  def domain;

  /**
   * Default option for argocd
   */
  def DEFAULT_OPTION = '--grpc-web';

  /**
   * Constructs a ArgoCd object with the provided script context.
   * 
   * @param script (reference) The script context to execute shell commands.
   * @param domain (String) The domain where argocd is hosted.
   * @param appName (String) The application name in argocd.
   */
  ArgoCd(script, domain, appName) {
    this.script  = script;
    this.appName = appName;
    this.domain  = domain;
  }

  /**
  * Login to the ArgoCD Server
  *
  * @param username (String) The username to authenticate.
  * @param password (String) The password of the give username.
  */
  void login(String username, String password) {
    script.sh("argocd login ${this.domain} --username ${username} --password ${password} ${this.DEFAULT_OPTION}")
  }

  /**
  * Logout of the ArgoCD Server
  */
  void logout() {
    script.sh("argocd logout ${this.domain}")
  }

  /**
  * Run any action on the ArgoCD Server
  *
  * @param action (String) The action to perform (eg. restart)
  * @param resource (String) The resource on which the action is performed. (eg. Deployment)
  * @param resourceName (String) The name of the resource which is being modified.
  * @param timeout (Number) Optional parameter to wait for the action to complete. (Default: 720)
  */
  void run(String action, String resource, String resourceName, Number timeout = 720) {
    script.sh("argocd app actions run ${this.appName} ${action} --kind ${resource} --resource-name ${resourceName} ${this.DEFAULT_OPTION}")
    waitForSync(timeout)
  }

  /**
  * Sync the argocd application
  *
  * @param resource (String) Optional parameter the name of the resource to sync. (e.g. :ConfigMap:configmap-name)
  * @param timeout (Number) Optional parameter to wait for the sync to complete. (Default: 720)
  */
  void sync(String resource = null, Number timeout = 720) {
    if (resource) {
      script.sh("argocd app sync ${this.appName} --resource ${resource} ${this.DEFAULT_OPTION}")
    } else {
      script.sh("argocd app sync ${this.appName} ${this.DEFAULT_OPTION}")
    }
  }

  /**
  * Wait for the action on the application to complete
  *
  * @param timeout (Number) Optional parameter to wait for the action to complete. (Default: 720)
  */
  void waitForSync(Number timeout = 720) {
    script.sh("argocd app wait ${this.appName} --health ${this.DEFAULT_OPTION} --timeout ${timeout}")
  }

  /**
  * Refresh the ArgoCd Cache and force the sync operation.
  * 
  * @param resource (String) Optional parameter the name of the resource to sync. (e.g. :ConfigMap:configmap-name)
  * @param timeout (Number) Optional parameter to wait for the sync to complete. (Default: 720)
  */
  void refreshAndSync(String resource = null, Number timeout = 720) {
    script.sh("argocd app get ${this.appName} --hard-refresh ${this.DEFAULT_OPTION}")
    sync(resource, timeout)
  }

  /**
   *  Hard Refresh the ArgoCd Cache and pull the updated state from the repositories.
   */
  void hardRefresh() {
    script.sh("argocd app get ${this.appName} --hard-refresh ${this.DEFAULT_OPTION}")
  }
}