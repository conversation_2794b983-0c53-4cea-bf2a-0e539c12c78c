package org.global

import groovy.json.JsonSlurper
import groovy.json.JsonBuilder

/**
 * EcsManager provides comprehensive AWS ECS management capabilities for Jenkins pipelines.
 * 
 * This class handles ECS service deployments, task definition management, deployment monitoring,
 * and rollback operations. It integrates with the existing CloudAuthManager for cross-account
 * authentication and follows established patterns from the Jenkins shared library.
 * 
 * Features:
 * - Task definition creation and registration
 * - ECS service deployment and updates
 * - Blue/green and rolling deployment strategies
 * - Deployment monitoring with health checks
 * - Automatic rollback capabilities
 * - CloudWatch logging integration
 * - Cross-account deployment support
 * 
 * <AUTHOR> Shared Library
 * @version 1.0.0
 */
class EcsManager {
  
  /**
   * Reference to the Jenkins script context for executing shell commands
   */
  def script
  
  /**
   * AWS region for ECS operations
   */
  String region
  
  /**
   * CloudAuthManager instance for AWS authentication
   */
  CloudAuthManager cloudAuthManager
  
  /**
   * JSON parser for AWS CLI responses
   */
  JsonSlurper jsonSlurper
  
  /**
   * Constructor for EcsManager
   * 
   * @param script Jenkins script context
   * @param region AWS region
   * @param cloudAuthManager CloudAuthManager instance for authentication
   */
  EcsManager(script, String region, CloudAuthManager cloudAuthManager) {
    this.script = script
    this.region = region
    this.cloudAuthManager = cloudAuthManager
    this.jsonSlurper = new JsonSlurper()
  }
  
  /**
   * Deploy a new ECS service or create if it doesn't exist
   * 
   * @param config Deployment configuration map
   * @return Map containing deployment results
   */
  Map deployService(Map config) {
    script.echo("[+] Starting ECS service deployment: ${config.serviceName}")
    
    try {
      // Check if service exists
      def serviceExists = checkServiceExists(config.clusterName, config.serviceName)
      
      if (serviceExists) {
        script.echo("[+] Service exists, performing update deployment")
        return updateService(config)
      } else {
        script.echo("[+] Service does not exist, creating new service")
        return createService(config)
      }
      
    } catch (Exception e) {
      script.error("[-] Failed to deploy ECS service: ${e.getMessage()}")
    }
  }
  
  /**
   * Update an existing ECS service
   * 
   * @param config Update configuration map
   * @return Map containing update results
   */
  Map updateService(Map config) {
    script.echo("[+] Updating ECS service: ${config.serviceName}")
    
    try {
      // Register new task definition
      def taskDefinitionArn = registerTaskDefinition(config)
      
      // Update service with new task definition
      def updateResult = updateServiceTaskDefinition(config, taskDefinitionArn)
      
      // Wait for deployment to complete if requested
      if (config.waitForDeployment) {
        waitForDeploymentCompletion(config, updateResult.deploymentId)
      }
      
      script.echo("[+] Service update completed successfully")
      return [
        taskDefinitionArn: taskDefinitionArn,
        deploymentId: updateResult.deploymentId,
        serviceArn: updateResult.serviceArn
      ]
      
    } catch (Exception e) {
      script.error("[-] Failed to update ECS service: ${e.getMessage()}")
    }
  }
  
  /**
   * Create a new ECS service
   * 
   * @param config Service configuration map
   * @return Map containing creation results
   */
  Map createService(Map config) {
    script.echo("[+] Creating new ECS service: ${config.serviceName}")
    
    try {
      // Register task definition
      def taskDefinitionArn = registerTaskDefinition(config)
      
      // Create CloudWatch log group if logging is enabled
      if (config.enableLogging) {
        createLogGroup(config.logGroupName, config.logRetentionDays)
      }
      
      // Create service
      def createResult = createEcsService(config, taskDefinitionArn)
      
      // Wait for service to become stable if requested
      if (config.waitForDeployment) {
        waitForServiceStable(config)
      }
      
      script.echo("[+] Service creation completed successfully")
      return [
        taskDefinitionArn: taskDefinitionArn,
        serviceArn: createResult.serviceArn,
        deploymentId: createResult.deploymentId
      ]
      
    } catch (Exception e) {
      script.error("[-] Failed to create ECS service: ${e.getMessage()}")
    }
  }
  
  /**
   * Scale an ECS service to the desired count
   * 
   * @param config Scaling configuration map
   * @return Map containing scaling results
   */
  Map scaleService(Map config) {
    script.echo("[+] Scaling ECS service: ${config.serviceName} to ${config.desiredCount} tasks")
    
    try {
      def scaleCommand = """
        aws ecs update-service \\
          --cluster ${config.clusterName} \\
          --service ${config.serviceName} \\
          --desired-count ${config.desiredCount} \\
          --region ${region} \\
          --output json
      """
      
      def result = script.sh(returnStdout: true, script: scaleCommand).trim()
      def scaleResult = jsonSlurper.parseText(result)
      
      if (config.waitForDeployment) {
        waitForServiceStable(config)
      }
      
      script.echo("[+] Service scaling completed successfully")
      return [
        serviceArn: scaleResult.service.serviceArn,
        desiredCount: scaleResult.service.desiredCount,
        runningCount: scaleResult.service.runningCount
      ]
      
    } catch (Exception e) {
      script.error("[-] Failed to scale ECS service: ${e.getMessage()}")
    }
  }
  
  /**
   * Rollback ECS service to previous task definition
   * 
   * @param config Rollback configuration map
   * @return Map containing rollback results
   */
  Map rollbackService(Map config) {
    script.echo "[+] Rolling back ECS service: ${config.serviceName}"
    
    try {
      // Get previous task definition
      def previousTaskDef = getPreviousTaskDefinition(config.taskDefinitionFamily)
      
      if (!previousTaskDef) {
        script.error("[-] No previous task definition found for rollback")
      }
      
      script.echo "[+] Rolling back to task definition: ${previousTaskDef}"
      
      // Update service with previous task definition
      def rollbackCommand = """
        aws ecs update-service \\
          --cluster ${config.clusterName} \\
          --service ${config.serviceName} \\
          --task-definition ${previousTaskDef} \\
          --region ${region} \\
          --output json
      """
      
      def result = script.sh(returnStdout: true, script: rollbackCommand).trim()
      def rollbackResult = jsonSlurper.parseText(result)
      
      if (config.waitForDeployment) {
        waitForDeploymentCompletion(config, rollbackResult.service.deployments[0].id)
      }
      
      script.echo "[+] Service rollback completed successfully"
      return [
        taskDefinitionArn: previousTaskDef,
        serviceArn: rollbackResult.service.serviceArn,
        deploymentId: rollbackResult.service.deployments[0].id
      ]
      
    } catch (Exception e) {
      script.error("[-] Failed to rollback ECS service: ${e.getMessage()}")
    }
  }
  
  /**
   * Register a new task definition with ECS
   * 
   * @param config Task definition configuration
   * @return String Task definition ARN
   */
  String registerTaskDefinition(Map config) {
    script.echo "[+] Registering task definition: ${config.taskDefinitionFamily}"
    
    try {
      // Build task definition JSON
      def taskDefinition = buildTaskDefinition(config)
      def taskDefJson = new JsonBuilder(taskDefinition).toPrettyString()
      
      // Write task definition to temporary file
      script.writeFile(file: 'task-definition.json', text: taskDefJson)
      
      // Register task definition
      def registerCommand = """
        aws ecs register-task-definition \\
          --cli-input-json file://task-definition.json \\
          --region ${region} \\
          --output json
      """
      
      def result = script.sh(returnStdout: true, script: registerCommand).trim()
      def registerResult = jsonSlurper.parseText(result)
      
      def taskDefinitionArn = registerResult.taskDefinition.taskDefinitionArn
      script.echo("[+] Task definition registered: ${taskDefinitionArn}")
      
      return taskDefinitionArn
      
    } catch (Exception e) {
      script.error("[-] Failed to register task definition: ${e.getMessage()}")
    }
  }
  
  /**
   * Build task definition JSON structure
   * 
   * @param config Configuration map
   * @return Map Task definition structure
   */
  Map buildTaskDefinition(Map config) {
    def containerDefinitions = [[
      name: config.serviceName,
      image: config.containerImage,
      essential: true,
      portMappings: config.portMappings ?: [],
      environment: buildEnvironmentVariables(config.environmentVariables),
      secrets: buildSecrets(config.secrets),
      logConfiguration: config.enableLogging ? [
        logDriver: 'awslogs',
        options: [
          'awslogs-group': config.logGroupName,
          'awslogs-region': region,
          'awslogs-stream-prefix': 'ecs'
        ]
      ] : null
    ]]
    
    def taskDefinition = [
      family: config.taskDefinitionFamily,
      networkMode: config.networkMode,
      requiresCompatibilities: config.requiresCompatibilities,
      cpu: config.cpu,
      memory: config.memory,
      containerDefinitions: containerDefinitions
    ]
    
    // Add execution role if provided
    if (config.executionRoleArn) {
      taskDefinition.executionRoleArn = config.executionRoleArn
    }
    
    // Add task role if provided
    if (config.taskRoleArn) {
      taskDefinition.taskRoleArn = config.taskRoleArn
    }
    
    // Add tags if provided
    if (config.tags && !config.tags.isEmpty()) {
      taskDefinition.tags = config.tags.collect { key, value ->
        [key: key, value: value]
      }
    }
    
    return taskDefinition
  }
  
  /**
   * Build environment variables array for container definition
   * 
   * @param envVars Map of environment variables
   * @return List Environment variables array
   */
  List buildEnvironmentVariables(Map envVars) {
    return envVars.collect { key, value ->
      [name: key, value: value.toString()]
    }
  }
  
  /**
   * Build secrets array for container definition
   *
   * @param secrets Map of secrets
   * @return List Secrets array
   */
  List buildSecrets(Map secrets) {
    return secrets.collect { key, valueFrom ->
      [name: key, valueFrom: valueFrom]
    }
  }

  /**
   * Create ECS service
   *
   * @param config Service configuration
   * @param taskDefinitionArn Task definition ARN
   * @return Map Service creation result
   */
  Map createEcsService(Map config, String taskDefinitionArn) {
    script.echo("[+] Creating ECS service with task definition: ${taskDefinitionArn}")

    def serviceDefinition = [
      serviceName: config.serviceName,
      cluster: config.clusterName,
      taskDefinition: taskDefinitionArn,
      desiredCount: config.desiredCount,
      launchType: config.requiresCompatibilities[0],
      deploymentConfiguration: [
        maximumPercent: config.maxHealthyPercent,
        minimumHealthyPercent: config.minHealthyPercent
      ],
      healthCheckGracePeriodSeconds: config.healthCheckGracePeriodSeconds
    ]

    // Add network configuration for awsvpc mode
    if (config.networkMode == 'awsvpc') {
      serviceDefinition.networkConfiguration = [
        awsvpcConfiguration: [
          subnets: config.subnets,
          securityGroups: config.securityGroups,
          assignPublicIp: config.assignPublicIp
        ]
      ]
    }

    // Add tags if provided
    if (config.tags && !config.tags.isEmpty()) {
      serviceDefinition.tags = config.tags.collect { key, value ->
        [key: key, value: value]
      }
    }

    def serviceJson = new JsonBuilder(serviceDefinition).toPrettyString()
    script.writeFile(file: 'service-definition.json', text: serviceJson)

    def createCommand = """
      aws ecs create-service \\
        --cli-input-json file://service-definition.json \\
        --region ${region} \\
        --output json
    """

    def result = script.sh(returnStdout: true, script: createCommand).trim()
    def createResult = jsonSlurper.parseText(result)

    return [
      serviceArn: createResult.service.serviceArn,
      deploymentId: createResult.service.deployments[0].id
    ]
  }

  /**
   * Update ECS service with new task definition
   *
   * @param config Service configuration
   * @param taskDefinitionArn New task definition ARN
   * @return Map Update result
   */
  Map updateServiceTaskDefinition(Map config, String taskDefinitionArn) {
    script.echo("[+] Updating service with new task definition: ${taskDefinitionArn}")

    def updateCommand = """
      aws ecs update-service \\
        --cluster ${config.clusterName} \\
        --service ${config.serviceName} \\
        --task-definition ${taskDefinitionArn} \\
        --desired-count ${config.desiredCount} \\
        --deployment-configuration maximumPercent=${config.maxHealthyPercent},minimumHealthyPercent=${config.minHealthyPercent} \\
        --region ${region} \\
        --output json
    """

    def result = script.sh(returnStdout: true, script: updateCommand).trim()
    def updateResult = jsonSlurper.parseText(result)

    return [
      serviceArn: updateResult.service.serviceArn,
      deploymentId: updateResult.service.deployments[0].id
    ]
  }

  /**
   * Check if ECS service exists
   *
   * @param clusterName Cluster name
   * @param serviceName Service name
   * @return Boolean True if service exists
   */
  Boolean checkServiceExists(String clusterName, String serviceName) {
    try {
      def describeCommand = """
        aws ecs describe-services \\
          --cluster ${clusterName} \\
          --services ${serviceName} \\
          --region ${region} \\
          --output json
      """

      def result = script.sh(returnStdout: true, script: describeCommand).trim()
      def describeResult = jsonSlurper.parseText(result)

      return describeResult.services &&
             !describeResult.services.isEmpty() &&
             describeResult.services[0].status != 'INACTIVE'

    } catch (Exception e) {
      script.echo("[-] Service check failed, assuming service does not exist: ${e.getMessage()}")
      return false
    }
  }

  /**
   * Get previous task definition for rollback
   *
   * @param family Task definition family
   * @return String Previous task definition ARN
   */
  String getPreviousTaskDefinition(String family) {
    try {
      def listCommand = """
        aws ecs list-task-definitions \\
          --family-prefix ${family} \\
          --status ACTIVE \\
          --sort DESC \\
          --max-items 2 \\
          --region ${region} \\
          --output json
      """

      def result = script.sh(returnStdout: true, script: listCommand).trim()
      def listResult = jsonSlurper.parseText(result)

      if (listResult.taskDefinitionArns && listResult.taskDefinitionArns.size() > 1) {
        return listResult.taskDefinitionArns[1] // Second most recent
      }

      return null

    } catch (Exception e) {
      script.echo("[-] Failed to get previous task definition: ${e.getMessage()}")
      return null
    }
  }

  /**
   * Wait for ECS deployment to complete
   *
   * @param config Configuration map
   * @param deploymentId Deployment ID to monitor
   */
  void waitForDeploymentCompletion(Map config, String deploymentId) {
    script.echo("[+] Waiting for deployment completion: ${deploymentId}")

    def timeoutMinutes = config.deploymentTimeoutMinutes ?: 15
    def timeoutSeconds = timeoutMinutes * 60
    def startTime = System.currentTimeMillis()

    while (true) {
      def currentTime = System.currentTimeMillis()
      def elapsedSeconds = (currentTime - startTime) / 1000

      if (elapsedSeconds > timeoutSeconds) {
        script.error("[-] Deployment timeout after ${timeoutMinutes} minutes")
      }

      def deploymentStatus = getDeploymentStatus(config.clusterName, config.serviceName, deploymentId)

      script.echo("[+] Deployment status: ${deploymentStatus.status} (${Math.round(elapsedSeconds)}s elapsed)")

      if (deploymentStatus.status == 'PRIMARY') {
        script.echo("[+] Deployment completed successfully")
        break
      } else if (deploymentStatus.status == 'FAILED') {
        script.error("[-] Deployment failed: ${deploymentStatus.reason}")
      }

      script.sleep(30) // Wait 30 seconds before next check
    }
  }

  /**
   * Wait for ECS service to become stable
   *
   * @param config Configuration map
   */
  void waitForServiceStable(Map config) {
    script.echo("[+] Waiting for service to become stable: ${config.serviceName}")

    def waitCommand = """
      aws ecs wait services-stable \\
        --cluster ${config.clusterName} \\
        --services ${config.serviceName} \\
        --region ${region}
    """

    script.timeout(time: config.deploymentTimeoutMinutes ?: 15, unit: 'MINUTES') {
      script.sh(script: waitCommand)
    }

    script.echo("[+] Service is now stable")
  }

  /**
   * Get deployment status for a specific deployment
   *
   * @param clusterName Cluster name
   * @param serviceName Service name
   * @param deploymentId Deployment ID
   * @return Map Deployment status information
   */
  Map getDeploymentStatus(String clusterName, String serviceName, String deploymentId) {
    def describeCommand = """
      aws ecs describe-services \\
        --cluster ${clusterName} \\
        --services ${serviceName} \\
        --region ${region} \\
        --output json
    """

    def result = script.sh(returnStdout: true, script: describeCommand).trim()
    def describeResult = jsonSlurper.parseText(result)

    if (describeResult.services && !describeResult.services.isEmpty()) {
      def service = describeResult.services[0]
      def deployment = service.deployments.find { it.id == deploymentId }

      if (deployment) {
        return [
          status: deployment.status,
          reason: deployment.reason ?: '',
          runningCount: deployment.runningCount,
          desiredCount: deployment.desiredCount,
          pendingCount: deployment.pendingCount
        ]
      }
    }

    return [status: 'UNKNOWN', reason: 'Deployment not found']
  }

  /**
   * Create CloudWatch log group for ECS service
   *
   * @param logGroupName Log group name
   * @param retentionDays Log retention in days
   */
  void createLogGroup(String logGroupName, Integer retentionDays) {
    script.echo("[+] Creating CloudWatch log group: ${logGroupName}")

    try {
      // Check if log group exists
      def describeCommand = """
        aws logs describe-log-groups \\
          --log-group-name-prefix ${logGroupName} \\
          --region ${region} \\
          --output json
      """

      def result = script.sh(returnStdout: true, script: describeCommand).trim()
      def describeResult = jsonSlurper.parseText(result)

      def logGroupExists = describeResult.logGroups.any { it.logGroupName == logGroupName }

      if (!logGroupExists) {
        // Create log group
        def createCommand = """
          aws logs create-log-group \\
            --log-group-name ${logGroupName} \\
            --region ${region}
        """

        script.sh(script: createCommand)
        script.echo("[+] Log group created: ${logGroupName}")

        // Set retention policy
        def retentionCommand = """
          aws logs put-retention-policy \\
            --log-group-name ${logGroupName} \\
            --retention-in-days ${retentionDays} \\
            --region ${region}
        """

        script.sh(script: retentionCommand)
        script.echo("[+] Log retention set to ${retentionDays} days")
      } else {
        script.echo("[+] Log group already exists: ${logGroupName}")
      }

    } catch (Exception e) {
      script.echo("[-] Failed to create log group: ${e.getMessage()}")
    }
  }

  /**
   * Get ECS service health status
   *
   * @param clusterName Cluster name
   * @param serviceName Service name
   * @return Map Service health information
   */
  Map getServiceHealth(String clusterName, String serviceName) {
    try {
      def describeCommand = """
        aws ecs describe-services \\
          --cluster ${clusterName} \\
          --services ${serviceName} \\
          --region ${region} \\
          --output json
      """

      def result = script.sh(returnStdout: true, script: describeCommand).trim()
      def describeResult = jsonSlurper.parseText(result)

      if (describeResult.services && !describeResult.services.isEmpty()) {
        def service = describeResult.services[0]
        return [
          serviceName: service.serviceName,
          status: service.status,
          runningCount: service.runningCount,
          pendingCount: service.pendingCount,
          desiredCount: service.desiredCount,
          taskDefinition: service.taskDefinition,
          healthy: service.runningCount == service.desiredCount
        ]
      }

      return [healthy: false, error: 'Service not found']

    } catch (Exception e) {
      return [healthy: false, error: e.getMessage()]
    }
  }
}
