package org.global

import org.global.CommonConstants

/**
 * A utility to interact with AWS Systems Manager Parameter Store.
 * This provides methods to retrieve and update parameters securely using
 * AWS CLI commands.
 */
class ParameterStore {

  /**
   * A reference to the script context, used to execute shell commands.
   */
  def script;

  /**
   * The default parameter type for storing values in Parameter Store.
   */
  def DEFAULT_TYPE = 'SecureString'

  /**
   * The default output type for retrieving parameters.
   */
  def DEFAULT_OUTPUT_TYPE = 'text'

  /**
   * Constructs a ParameterStore object with the provided script context.
   * 
   * @param script (reference) The script context to execute shell commands.
   */
  ParameterStore(script) {
    this.script = script;
  }

  /**
   * Retrieves a parameter from AWS Systems Manager Parameter Store.
   * 
   * @param parameterPath (String) The name of the parameter to retrieve.
   * @param parameterQuery (String) The specific attribute of the parameter to query (default: 'Parameter.Value').
   * @param region (String) The AWS region where the parameter is stored (default: region from CommonConstants).
   * @return parameter (String) The value of the specified parameter as a string.
   */
  String getParameter(String parameterPath, String parameterQuery = 'Parameter.Value', String region = CommonConstants.DEFAULTS.DEFAULT_REGION) {
    def parameterOutput = script.sh(script: "aws ssm get-parameter --name ${parameterPath} --with-decryption --output ${this.DEFAULT_OUTPUT_TYPE} --query ${parameterQuery} --region ${region}", returnStdout: true).trim()
    return parameterOutput
  }

  /**
   * Updates or creates a parameter in AWS Systems Manager Parameter Store.
   * 
   * @param parameterPath (String) The name of the parameter to update or create.
   * @param parameterValue (String) The value to store in the parameter.
   * @param type (String) The type of the parameter (default: 'SecureString').
   * @param region (String) The AWS region where the parameter is stored (default: region from CommonConstants).
   */
  String putParameter(String parameterPath, String parameterValue, String type = this.DEFAULT_TYPE, String region = CommonConstants.DEFAULTS.DEFAULT_REGION) {
    script.sh(script: "aws ssm put-parameter --name ${parameterPath} --value '${parameterValue}' --type ${type} --overwrite --region ${region}")
  }
}