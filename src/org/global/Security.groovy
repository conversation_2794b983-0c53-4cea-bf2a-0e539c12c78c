package org.global

/**
* Security related functions for scanning and analyzing Docker images and code
* Currently supports:
* - Trivy vulnerability scanning for Docker images
*/
class Security {
    def script

    /**
    * Constructor for the Security class
    *
    * @param script (Script) The Jenkins script object
    */
    Security(script) {
        this.script = script
    }

    /**
    * Perform Trivy security scan on a Docker image and generate reports
    *
    * @param repo (String) The docker repository to scan
    * @param outputPath (String) path to save the HTML report.
    * @param tag (String) The tag of the image to scan. Default: 'latest'
    */
    def trivyScan(String repo, String outputPath, String tag = 'latest') {
        // Download the HTML template for Trivy reports
        script.sh 'wget https://raw.githubusercontent.com/aquasecurity/trivy/refs/heads/main/contrib/html.tpl'
        script.sh 'trivy clean --scan-cache'
        script.sh "trivy image ${repo}:${tag} --format template --template @html.tpl --output ${outputPath}"
        def report_text = script.sh script: "trivy image ${repo}:${tag} --format json", returnStdout: true
        // remove the template file
        script.sh 'rm html.tpl'
        def report = script.readJSON(text: report_text)        
        return report
    }

}