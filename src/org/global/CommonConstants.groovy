package org.global

/**
 * A utility to store values across entire organization.
 */
class CommonConstants {
  /**
  * The default values map across all orgs.
  */
  static final Map<String, Object> DEFAULTS = [
    GIT_EMAIL: "<EMAIL>",
    GIT_USERNAME: "saasdevops",
    DEFAULT_REGION: "us-east-1",
    DEFAULT_BRANCH: "master",
    REGION_CLOUD_MAP: [
      "us-east-1": "AWS",
      "us-east-2": "AWS",
      "eu-central-1": "AWS",
      "ap-south-1": "AWS",
      "me-central-1": "AWS",
      "eu-west-2": "AWS",
      "ap-southeast-2": "AWS",
      "ca-central-1": "AWS",
      "me-central-2": "GCP"
    ],
    REGION_DC_MAP: [
      "us-east-1": "US",
      "eu-central-1": "EU",
      "ap-south-1": "AP",
      "me-central-1": "ME",
      "eu-west-2": "UK",
      "ap-southeast-2": "SY",
      "ca-central-1": "CA",
      "me-central2-b": "KSA"
    ],
    DATA_CENTER_REGION_MAP: [
      "US-VIRGINIA": "us-east-1",
      "EU-FRANKFURT": "eu-central-1",
      "AP-MUMBAI": "ap-south-1",
      "ME-UAE": "me-central-1",
      "UK-LONDON": "eu-west-2",
      "AU-SYDNEY": "ap-southeast-2",
      "CA-CANADA": "ca-central-1"
    ],
    DC_REGION_MAP: [
      "US": "us-east-1",
      "EU": "eu-central-1",
      "AP": "ap-south-1",
      "ME": "me-central-1",
      "UK": "eu-west-2",
      "SY": "ap-southeast-2",
      "CA": "ca-central-1",
      "KSA": "me-central-2b"
    ],
    SHARED_LIBRARY: [
      "repoUrl": "*****************:surveysparrow/sparrow-jenkins-shared-library.git",
      "folder": "sparrow-jenkins-shared-library",
      "cloudFlareProjectName": 'jenkinssharedlibraries'
    ],
    K8S_MANIFESTS: [
      "repoUrl": "*****************:surveysparrow/sparrow-k8s-manifests.git",
      "folder": "sparrow-k8s-manifests",
    ],
    PIPELINES: [
      "repoUrl": "*****************:surveysparrow/sparrow-pipeline.git",
      "folder": "sparrow-pipeline"
    ],
    TOFU_MODULES: [
      "repoUrl": "*****************:surveysparrow/sparrow-tofu-modules.git",
      "folder": "sparrow-tofu-modules"
    ],
    INFRACONFIG: [
      "repoUrl": "*****************:surveysparrow/sparrow-infra-config.git",
      "folder": "sparrow-infra-config"
    ],
    COMMON_GIT_REPOS: [
      "eksRepoUrl": "*****************:surveysparrow/sparrow-k8s-manifests.git"
    ],
    EMAIL: [
      "sender": "<EMAIL>",
      "recipient": "<EMAIL>"
    ],
    ARGOCD: [
      "credentialId": "SurveySparrowArgoCD",
      "ACTIONS": [
        "deploy": "KUSTOMIZE_DEPLOY",
        "restart": "RESTART",
        "configUpdate": "KUSTOMIZE_CONFIG_UPDATE",
        "syncKustomizeApp": "SYNC_KUSTOMIZE",
      ]
    ]
  ]
}