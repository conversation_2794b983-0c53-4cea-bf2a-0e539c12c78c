package org.global

/**
 * A utility to interact with Cloudflare API.
 * This provides methods to manage Cloudflare cache and other operations
 * through the Cloudflare REST API.
 */
class Cloudflare {
    /**
     * A reference to the script context, used to execute shell commands.
     */
    def script
    
    /**
     * Cloudflare Zone ID
     */
    String zoneId
    
    /**
     * Cloudflare API token
     */
    String apiToken

    /**
     * Cloudflare Account ID
     */
    String accountId = ""
    
    /**
     * Constructs a Cloudflare object with the provided script context.
     * 
     * @param script (reference) The script context to execute shell commands.
     * @param zoneId (String) The Cloudflare zone ID for the domain.
     * @param apiToken (String) The Cloudflare API token with required permissions.
     */
    Cloudflare(script, String zoneId, String apiToken, String accountId="") {
        this.script = script
        this.zoneId = zoneId
        this.apiToken = apiToken
        this.accountId = accountId
    }
    
    /**
     * Purges Cloudflare cache for specified domains and paths.
     *
     * @param cacheType (String) MANDATORY The type of cache purge to perform: 'hosts', 'prefixes', or 'files'
     * @param domains (List<String>) MANDATORY List of domains for which to purge cache
     * @param paths (List<String>) OPTIONAL List of paths to purge. Default is an empty path [""]
     * @return (String) The response from the API call
     * 
     * @example
     * def cloudflare = new Cloudflare(this, "zone123456", "${CLOUDFLARE_API_TOKEN}")
     * cloudflare.purgeCache(
     *   "prefixes",
     *   ["example.com", "www.example.com"],
     *   ["/blog", "/products"]
     * )
     */
    String purgeCache(String cacheType, List<String> domains, List<String> paths = [""]) {
        // Build the list of domain+path combinations based on cache type
        def domainsPath = []
        
        for (String domain in domains) {
            for (String path in paths) {
                if (cacheType == 'files') {
                    domainsPath.add("\"https://${domain}${path}\"")
                } else {
                    domainsPath.add("\"${domain}${path}\"")
                }
            }
        }
        
        def domainsList = domainsPath.join(", ")
        def apiUrl = "https://api.cloudflare.com/client/v4/zones/${this.zoneId}/purge_cache"
        def payload = "{\"${cacheType}\": [${domainsList}]}"
        
        return script.sh(
            script: """
                curl --request POST \
                --url ${apiUrl} \
                --header 'Content-Type: application/json' \
                --header 'Authorization: Bearer ${this.apiToken}' \
                --data '${payload}'
            """,
            returnStdout: true
        ).trim()
    }

    /**
     * Installs the Wrangler CLI for Cloudflare Pages deployment.
     *
     * @param version (String) The version of Wrangler to install. Default is "4.13.2".
     */
    def installWrangler(String version = "4.13.2") {
        script.sh "npm install wrangler@${version} --save-dev"
    }

    /**
     * Creates a Cloudflare Pages project
     *
     * @param projectName (String) The name of the project to create.
     * @param productionBranch (String) The branch to use for production deployments.
     */
    def createPagesProject(String projectName, String productionBranch) {
        script.sh "CLOUDFLARE_ACCOUNT_ID=${this.accountId} CLOUDFLARE_API_TOKEN=${this.apiToken} npx wrangler pages project create ${projectName} --production-branch ${productionBranch}"
    }

    /**
     * Deploys a Cloudflare Pages project.
     * @param projectName (String) The name of the project to deploy.
     * @param buildDir (String) The directory containing the build output.
     * @param branch (String) The branch to deploy.
     */
    def deployPages(String projectName, String buildDir, String branch) {
        script.sh "CLOUDFLARE_ACCOUNT_ID=${this.accountId} CLOUDFLARE_API_TOKEN=${this.apiToken} npx wrangler pages deploy ${buildDir} --project-name ${projectName} --branch ${branch}"
    }

    /**
    * Check if the pages project exists
    *
    * @param projectName (String) The name of the project to check.
    * @return (Boolean) True if the project exists, false otherwise.
    */
    Boolean doesPagesProjectExist(String projectName) {
        def doesPagesProjectExist = script.sh(script: "CLOUDFLARE_ACCOUNT_ID=${this.accountId} CLOUDFLARE_API_TOKEN=${this.apiToken} npx wrangler pages project list | grep -w ${projectName}", returnStatus: true) == 0
        return doesPagesProjectExist
    }

    /**
     * Add a custom domain to the project
     *
     * @param projectName (String) The name of the project to add the custom domain to.
     * @param customDomain (String) The custom domain to add to the project.
     */
    def addCustomDomain(String projectName, String customDomain) {
        script.sh """
            curl --request POST \
            --url https://api.cloudflare.com/client/v4/accounts/${this.accountId}/pages/projects/${projectName}/domains \
            --header 'Content-Type: application/json' \
            --header 'Authorization: Bearer ${this.apiToken}' \
            -d '{"name": "${customDomain}"}'
        """
    }

    /**
      * Create pages project if it doesn't exist
      *
      * @param projectName (String) The name of the project to create.
      * @param productionBranch (String) The branch to use for production deployments.
      * @param customDomain (String) The custom domain to add to the project.
      */
    def createPagesProjectIfNotExist(String projectName, String productionBranch, String customDomain = null) {
        if (!doesPagesProjectExist(projectName)) {
            createPagesProject(projectName, productionBranch)
            if (customDomain) {
                addCustomDomain(projectName, customDomain)
            }
        }
    }
}
