package org.global

/**
 * A utility to interact with AWS EC2.
 * This provides methods to manage EC2 instances, AMIs, and snapshots
 * through AWS CLI commands.
 */
class Ec2 {
    /**
     * A reference to the script context, used to execute shell commands.
     */
    def script

    Ec2(script) {
        this.script = script
    }

    /**
     * Get EC2 instance ID by tag name
     *
     * @param tagName (String) MANDATORY The tag name to search for
     * @return (String) The instance ID of the matching EC2 instance
     */
    String getInstanceIdByTag(String tagName) {
        def instanceIdCmd = """
            aws ec2 describe-instances \
            --filters "Name=tag:Name,Values=${tagName}" \
            --query "Reservations[0].Instances[0].InstanceId" \
            --output text
        """
        return script.sh(script: instanceIdCmd, returnStdout: true).trim()
    }

    /**
     * Create an AMI backup from an EC2 instance
     * 
     * @param instanceId (String) MANDATORY The ID of the EC2 instance to backup
     * @param amiName (String) MANDATORY The name to give the AMI
     * @param description (String) MANDATORY Description for the AMI
     * @param tags (String) MANDATORY Tags to apply to the AMI in AWS CLI format
     * @return (String) The ID of the created AMI
     */
    String createAmiBackup(String instanceId, String amiName, String description, String tags) {
        def createAmiCmd = """
            aws ec2 create-image \
            --instance-id ${instanceId} \
            --name '${amiName}' \
            --description '${description}' \
            --no-reboot \
            --query 'ImageId' \
            --tag-specifications ${tags} \
            --output text
        """
        return script.sh(script: createAmiCmd, returnStdout: true).trim()
    }

    /**
     * Wait for an AMI to become available
     *
     * @param amiId (String) MANDATORY The ID of the AMI to wait for
     * @param region (String) MANDATORY The AWS region where the AMI exists
     */
    void waitForAmiAvailable(String amiId, String region) {
        script.sh """
            sleep 250
            aws ec2 wait image-available --image-ids ${amiId} --region ${region}
        """
    }

    /**
     * List AMIs with a specific name prefix
     *
     * @param namePrefix (String) MANDATORY The prefix to filter AMI names
     * @return (List) List of AMIs with their ImageId, Name and CreationDate
     */
    def listAmis(String namePrefix) {
        def listAmiCmd = """
            aws ec2 describe-images \
            --owners self \
            --filters "Name=name,Values=${namePrefix}*" \
            --query "sort_by(Images, &CreationDate)[*].{ImageId:ImageId,Name:Name,CreationDate:CreationDate}" \
            --output json
        """
        return script.readJSON(text: script.sh(script: listAmiCmd, returnStdout: true).trim())
    }

    /**
     * Get snapshot IDs associated with an AMI
     *
     * @param amiId (String) MANDATORY The ID of the AMI
     * @return (List) List of snapshot IDs associated with the AMI
     */
    def getAmiSnapshots(String amiId) {
        def getSnapshotsCmd = """
            aws ec2 describe-images \
            --image-ids ${amiId} \
            --query 'Images[0].BlockDeviceMappings[*].Ebs.SnapshotId' \
            --output json
        """
        return script.readJSON(text: script.sh(script: getSnapshotsCmd, returnStdout: true).trim())
    }

    /**
     * Deregister an AMI
     *
     * @param amiId (String) MANDATORY The ID of the AMI to deregister
     */
    void deregisterAmi(String amiId) {
        script.sh "aws ec2 deregister-image --image-id ${amiId}"
    }

    /**
     * Delete an EBS snapshot
     *
     * @param snapshotId (String) MANDATORY The ID of the snapshot to delete
     */
    void deleteSnapshot(String snapshotId) {
        script.sh "aws ec2 delete-snapshot --snapshot-id ${snapshotId}"
    }
}