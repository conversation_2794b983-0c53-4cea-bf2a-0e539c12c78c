package org.global

import org.global.CommonConstants

/**
 * A utility to interact with AWS S3.
 * This provides methods to upload files, download files and check if a given file exists
 * through AWS CLI commands.
 */
class S3 {
  /**
   * A reference to the script context, used to execute shell commands.
   */
  def script;

  S3(script) {
    this.script = script;
  }

  /**
   * Uploads a given file to an S3 bucket
   * 
   * @param localFilePath (String) The path where the file is located.
   * @param bucket (String) The name of the S3 bucket to push the files to.
   * @param s3Path (String) The path where to upload the file in the given S3 bucket.
   * @param region (String) The AWS region where the S3 bucket is located (default: region from CommonConstants).
   */
  void uploadFile(String localFilePath, String bucket, String s3Path, String region = CommonConstants.DEFAULTS.DEFAULT_REGION) {
    if (!script.fileExists(localFilePath)) {
      println "[+] File doesn't exist!"
      script.error("File in ${localFilePath} does not exist")
    } else {
      script.sh(script: "aws s3 cp ${localFilePath} s3://${bucket}/${s3Path} --region ${region} --quiet")
    }
  }

  /**
   * Downloads a given file from an S3 bucket
   * 
   * @param s3Path (String) The path where to the file is located in the given S3 bucket.
   * @param bucket (String) The name of the S3 bucket to retrieve the files from.
   * @param localFilePath (String) The path where to download the files.
   * @param region (String) The AWS region where the S3 bucket is located (default: region from CommonConstants).
   */
  void downloadFile(String s3Path, String bucket, String localFilePath = "./", String region = CommonConstants.DEFAULTS.DEFAULT_REGION) {
    script.sh(script: "aws s3 cp s3://${bucket}/${s3Path} ${localFilePath} --region ${region} --quiet")  
  }

  /**
   * Check if a file or folder exists in a S3 bucket
   * 
   * @param fileOrFolder (String) The path where the file is located.
   * @param s3Path (String) The path where the file is in the given S3 bucket.
   * @param bucket (String) The name of the S3 bucket.
   * @param region (String) The AWS region where the S3 bucket is located (default: region from CommonConstants).
   */
  boolean fileOrFolderExists(String fileOrFolder, String s3Path, String bucket, String region = CommonConstants.DEFAULTS.DEFAULT_REGION) {
    def fileOrFolderExists = script.sh(script: "aws s3 ls s3://${bucket}/${s3Path} | grep ${fileOrFolder} | tr -d '\n'", returnStdout: true)
    return fileOrFolderExists == '' ? false : true
  }

   /**
   * Generates a pre-signed URL for a given file in an S3 bucket
   *
   * @param s3Path (String) The path where the file is located in the given S3 bucket.
   * @param bucket (String) The name of the S3 bucket to retrieve the files from.
   *  @param expiry (int) The expiry time for the pre-signed URL (default: 3600 seconds).
   * @param region (String) The AWS region where the S3 bucket is located (default: region from CommonConstants).
   */
  String getPreSignedUrl(String s3Path, String bucket, int expiry = 3600, String region = CommonConstants.DEFAULTS.DEFAULT_REGION) {
    def presignedUrl = script.sh(script: "aws s3 presign s3://${bucket}/${s3Path} --expires-in ${expiry} --region ${region}", returnStdout: true).trim()
    return presignedUrl
  }
}
