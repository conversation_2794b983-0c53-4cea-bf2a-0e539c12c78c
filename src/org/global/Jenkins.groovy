package org.global

import org.global.CommonConstants

/**
 * A utility to interact with Jenkins API.
 * This provides methods to manage Jenkins operations and configurations
 * through Jenkins REST API.
 */
class Jenkins {
    /**
     * A reference to the script context, used to execute shell commands.
     */
    def script

    <PERSON>(script) {
        this.script = script
    }

    /**
     * Make a Jenkins API call with specified parameters
     *
     * @param params (Map) MANDATORY Map containing API call parameters:
     *        - endpoint: API endpoint path
     *        - method: HTTP method (default: POST)
     *        - payload: Request payload (optional)
     *        - contentType: Content type header (default: application/json)
     *        - headers: Additional headers (optional)
     * @return (String) The response from the API call
     */

    String jenkinsApiCall(Map params) {
        String endpoint = params.endpoint
        String jenkinsApiKey = params.jenkinsApiKey
        String jenkinsUrl = params.jenkinsUrl
        String method = params.method ?: 'POST'
        String payload = params.payload ?: ''
        String contentType = params.contentType ?: 'application/json'
        String additionalHeaders = params.headers ?: ''
        
        // Validate required parameters to prevent curl errors
        if (!jenkinsUrl) {
            throw new IllegalArgumentException("jenkinsUrl is required for API call")
        }
        if (!endpoint) {
            throw new IllegalArgumentException("endpoint is required for API call")
        }

        String curlCmd = """curl -s -u """
        
        curlCmd += """ ${jenkinsApiKey} """
        
        if (payload) {
            curlCmd += """ -H "Content-Type: ${contentType}" """
        }
        
        if (additionalHeaders) {
            curlCmd += " ${additionalHeaders}"
        }
        
        curlCmd += """ -X ${method}"""
        
        if (payload) {
            curlCmd += """ -d '${payload}'"""
        }
        
        curlCmd += """ ${jenkinsUrl}/${endpoint}"""

        script.echo "Executing: ${curlCmd}"
        def response = script.sh(script: curlCmd, returnStdout: true).trim()
        script.echo "Response: ${response}"

        return response
    }

    /**
     * Check if Jenkins service is active
     * @param quiet (boolean) Whether to run the command in quiet mode (default: true)
     * @return (boolean) True if Jenkins is active, false otherwise
     */
    private boolean isJenkinsActive(boolean quiet = true) {
        def quietFlag = quiet ? '--quiet' : ''
        def status = script.sh(
            script: "systemctl is-active ${quietFlag} jenkins",
            returnStatus: true
        )
        return status == 0
    }

    /**
     * Stop Jenkins service and wait for it to be fully non operational
     */
    void stopService() {
        script.sh "sudo systemctl stop jenkins"
        
        // Wait until Jenkins is fully stopped
        while (isJenkinsActive()) {
            script.sleep 2
            script.echo "Waiting for Jenkins to stop..."
        }
        script.echo "Jenkins stopped successfully"
    }

    /**
     * Start Jenkins service and wait for it to be fully operational
     */
    void startService() {
        script.sh "sudo systemctl start jenkins"

         // Wait until Jenkins is fully stopped
        while (isJenkinsActive()) {
            script.sleep 2
            script.echo "Waiting for Jenkins to start..."
        }
        script.echo "Jenkins started successfully"
    }

    /**
     * Reload Jenkins configuration
     * 
     * @param jenkinsUrl (String) The URL of the Jenkins instance
     * @param jenkinsApiKey (String) The API key for authentication
     */
    String reloadConfiguration(String jenkinsUrl, String jenkinsApiKey) {
        def response = jenkinsApiCall([
            endpoint: 'manage/configuration-as-code/reload',
            method: 'POST',
            jenkinsUrl: "${jenkinsUrl}",
            jenkinsApiKey: "${jenkinsApiKey}"
        ])
        return response
    }

    /**
     * View Export Jenkins configuration
     * 
     * @param jenkinsUrl (String) The URL of the Jenkins instance
     * @param jenkinsApiKey (String) The API key for authentication
     */
    void viewExportConfiguration(String jenkinsUrl, String jenkinsApiKey) {
        jenkinsApiCall([
            endpoint: 'manage/configuration-as-code/viewExport',
            method: 'POST',
            jenkinsUrl: "${jenkinsUrl}",
            jenkinsApiKey: "${jenkinsApiKey}"
        ])
    }

    /**
     * Safe restart Jenkins
     * 
     * @param jenkinsUrl (String) The URL of the Jenkins instance
     * @param jenkinsApiKey (String) The API key for authentication
     */
    void safeRestart(String jenkinsUrl, String jenkinsApiKey) {
        jenkinsApiCall([
            endpoint: 'safeRestart',
            method: 'POST',
            jenkinsUrl: "${jenkinsUrl}",
            jenkinsApiKey: "${jenkinsApiKey}"
        ])
    }
}