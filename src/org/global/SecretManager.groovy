package org.global

import org.global.CommonConstants

/**
 * A utility to interact with AWS Secrets Manager.
 * This provides methods to fetch secrets from AWS Secrets Manager.
 */
class SecretManager {
  /**
   * A reference to the script context, used to execute shell commands.
   */
  def script;

  SecretManager(script) {
    this.script = script;
  }

  /**
   * Fetches secrets from AWS Secrets Manager
   * 
   * @param secretName (String) The name of the secret to fetch.
   * @param region (String) The AWS region where the secret is located (default: region from CommonConstants).
   * @return (String) The secret value in JSON format
   */
  String fetchSecrets(String secretName, String region = CommonConstants.DEFAULTS.DEFAULT_REGION) {
    return script.sh(
      script: """
        aws secretsmanager get-secret-value \
        --secret-id ${secretName} \
        --region ${region} \
        --query 'SecretString' \
        --output text
      """,
      returnStdout: true
    ).trim()
  }
}