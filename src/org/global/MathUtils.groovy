package org.global

class MathUtils {
  /**
    * Calculate the percentage.
    * @param numerator (Number) the numerator
    * @param denominator (Number) the denominator
    * @return the percentage (Number) (rounded to two decimal places) or an error message if denominator is 0
    */
  static double calculatePercentage(Number numerator, Number denominator) {
    if (denominator == 0) {
      throw new IllegalArgumentException("Denominator cannot be zero")
    }

    BigDecimal percentage = (numerator.doubleValue() / denominator.doubleValue()) * 100
    return percentage.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue()
  }
}
