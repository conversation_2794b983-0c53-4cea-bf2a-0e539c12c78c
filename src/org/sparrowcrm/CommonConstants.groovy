package org.sparrowgenie

/**
 * Common constants used across SparrowCRM Jenkins pipelines.
 * Contains configuration maps for deployment parameters, status mappings,
 * region configurations, and other shared constants.
 */
class CommonConstants {

  // Constants for commonly used values to avoid duplication
  private static final String AWS_CLOUD = 'AWS'
  private static final String GCP_CLOUD = 'GCP'
  private static final String FAILURE_COLOR = '#FF4444'
  private static final String INFO_COLOR = '#FFAA66'
  private static final String BOMB_EMOJI = ':bomb:'

  static final Map<String, Object> DEFAULTS = [
    'BITBUCKET_CREDENTIAL_ID': 'BitBucketPrivateKey',
    'BITBUCKET_WORKSPACE': 'surveysparrow',
    'DEPLOYMENT_PARAMETERS': ['deployment', 'service', 'version', 'image', 'app', 'configmap'],
    'DEFAULT_JIRA_PROJECT_KEY': 'SSE',
    'SLACK_TOKEN_ID': 'SlackIntegrationToken',
    'SLACK_BOT_TOKEN_ID': 'SlackBotToken',
    'STATUS_EMOJI_MAP': [
      'STARTED': ':rocket:',
      'FAILURE': BOMB_EMOJI,
      'SUCCESS': ':pirate_flag:',
      'FAILED': BOMB_EMOJI,
      'ABORTED': ':dizzy_face:',
      'INFO': ':scroll:',
      'APPROVAL': ':hourglass:'
    ],
    'STATUS_COLOR_MAP': [
      'SUCCESS': '#44FF44',
      'FAILURE': FAILURE_COLOR,
      'FAILED': FAILURE_COLOR,
      'ABORTED': '#FFA500',
      'INFO': INFO_COLOR,
      'STARTED': '#4444FF',
      'APPROVAL': INFO_COLOR
    ],
    'REGION_CLOUD_MAP': [
      'us-east-1': AWS_CLOUD,
      'us-east-2': AWS_CLOUD,
      'eu-central-1': AWS_CLOUD,
      'ap-south-1': AWS_CLOUD,
      'me-central-1': AWS_CLOUD,
      'eu-west-2': AWS_CLOUD,
      'ap-southeast-2': AWS_CLOUD,
      'ca-central-1': AWS_CLOUD,
      'me-central-2': GCP_CLOUD
    ],
    'SPARROW_PIPELINE': [
      'repoUrl': '*****************:surveysparrow/sparrow-pipeline.git',
      'folder': 'sparrow-pipeline'
    ],
    'DEFAULT_REGION': 'us-east-1',
    'GIT': [
      'websiteRepo': '*****************:surveysparrow/crm-website.git'
    ]
  ]

}
