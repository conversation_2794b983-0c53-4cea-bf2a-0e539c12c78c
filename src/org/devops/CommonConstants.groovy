package org.devops

class CommonConstants {
  static final Map<String, Object> DEFAULTS = [
    "BITBUCKET_CREDENTIAL_ID": "BitBucketPrivateKey",
    "BITBUCKET_WORKSPACE": "surveysparrow",
    "DEPLOYMENT_PARAMETERS": [ "deployment", "service", "version", "image", "app", "configmap" ],
    "DEFAULT_JIRA_PROJECT_KEY": "SSE",
    "SLACK_TOKEN_ID": "SlackIntegrationToken",
    "SLACK_BOT_TOKEN_ID": "SlackBotToken",
    "STATUS_EMOJI_MAP": [
      "STARTED": ":rocket:",
      "FAILURE": ":bomb:",
      "SUCCESS": ":pirate_flag:",
      "FAILED": ":bomb:",
      "ABORTED": ":dizzy_face:",
      "INFO": ":scroll:",
      "APPROVAL": ":hourglass:"
    ],
    "STATUS_COLOR_MAP": [
      "SUCCESS": "#44FF44",
      "FAILURE": "#FF4444",
      "FAILED": "#FF4444",
      "ABORTED": "#FFA500",
      "INFO": "#FFAA66",
      "STARTED": "#4444FF",
      "APPROVAL": "#FFAA66"
    ],
    "REGION_CLOUD_MAP": [
      "us-east-1": "AWS",
      "eu-central-1": "AWS",
      "ap-south-1": "AWS",
      "me-central-1": "AWS",
      "eu-west-2": "AWS",
      "ap-southeast-2": "AWS",
      "ca-central-1": "AWS",
      "me-central-2": "GCP"
    ],
    "SPARROW_PIPELINE": [
      "repoUrl": "*****************:surveysparrow/sparrow-pipeline.git",
      "folder": "sparrow-pipeline"
    ],
    "DEFAULT_REGION": "us-east-1",
    "DEFAULT_JENKINS_REGION": "us-east-2",
    "DEFAULT_BRANCH": "master",
    "ARGOCD": [
      "username": "admin",
      "domain": "argocd.campaignsparrow.com",
      "ACTIONS": [
        "deploy": "KUSTOMIZE_DEPLOY",
        "restart": "RESTART",
        "config_update": "CONFIG_UPDATE"
      ]
    ],
    "JENKINS_COMMON_CONFIG": [
      "slackChannel": "common-jenkins-alerts",
      "JENKINS_JOB": [
        "jenkinsJobsDir": "/var/lib/jenkins/jobs",
        "jenkinsConfigFile": "/var/lib/jenkins/jenkins.yaml",
        "jenkinsConfigBackup": "/var/lib/jenkins/ConfigAsCodeBackup"
      ],
      "JENKINS_BACKUP": [
        "jenkinsMaster": "Jenkins-Master",
        "amiNamePrefix": "Jenkins-Master-Backup"
      ]
    ]
  ]
}