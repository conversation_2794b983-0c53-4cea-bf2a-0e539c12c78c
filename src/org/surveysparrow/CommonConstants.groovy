package org.surveysparrow

class CommonConstants {
  static final Map<String, Object> DEFAULTS = [
    "BITBUCKET_CREDENTIAL_ID": "BitBucketPrivateKey",
    "BITBUCKET_WORKSPACE": "surveysparrow",
    "DEPLOYMENT_PARAMETERS": [ "deployment", "service", "version", "image", "app", "configmap" ],
    "DEFAULT_JIRA_PROJECT_KEY": "SSE",
    "SLACK_TOKEN_ID": "SlackIntegrationToken",
    "SLACK_BOT_TOKEN_ID": "SlackBotToken",
    "REPORT_BUCKET": "reports.automatesparrow.com",
    "COMMON_REGISTRY_ENDPOINT": "713859105457.dkr.ecr.us-east-1.amazonaws.com",
    "STATUS_EMOJI_MAP": [
      "STARTED": ":rocket:",
      "FAILURE": ":bomb:",
      "SUCCESS": ":pirate_flag:",
      "FAILED": ":bomb:",
      "ABORTED": ":dizzy_face:",
      "INFO": ":scroll:",
      "APPROVAL": ":hourglass:"
    ],
    "STATUS_COLOR_MAP": [
      "SUCCESS": "#44FF44",
      "FAILURE": "#FF4444",
      "FAILED": "#FF4444",
      "ABORTED": "#FFA500",
      "INFO": "#FFAA66",
      "STARTED": "#4444FF",
      "APPROVAL": "#FFAA66"
    ],
    "SUBMODULES": [
      "app-v1": [ "surveysparrow-analyze", "surveysparrow-billing", "surveysparrow-integrations", "surveysparrow-external-events", "platform-services", "surveysparrow-db-models", "surveysparrow-operations", "ss-micro-comm-js-sdk" ],
      "ss-reputation-backend": [ "surveysparrow-db-models", "ss-micro-comm-js-sdk" ],
      "ss-ticket-backend": [ "surveysparrow-db-models", "ss-micro-comm-js-sdk" ],
      "ss-integrations-backend": [ "surveysparrow-db-models", "ss-micro-comm-js-sdk" ],
      "ss-eui-backend": [ "surveysparrow-db-models" ],
      "ss-analyze-backend": [ "surveysparrow-db-models", "ss-micro-comm-js-sdk" ],
      "ss-eui-frontend": []
    ],
    "CLOUDFLARE_TOKEN_ID": "CloudflareSecretToken",
    "CODE_MAGIC_API_TOKEN_ID": "CodeMagicAPIToken",
    "VERSION_BUMP_TYPES": [ "patch", "minor", "major"],
    "BITBUCKET_TOKEN_BASE64": "BitBucketOAuthToken",
    "PRODUCTION_AWS_ACCOUNT_ID": "************",
    "MOBILE_IAM_ROLE_NAME": "MobileParameterStoreAccessRole",
    "APP_V1_CRUCIAL_ASSETS": [
      "admin.app.bundle.js", "admin.helpers.app.bundle.js", "admin.vendors.app.bundle.js", "Auth.app.bundle.js", "CameraInput.app.bundle.js",
      "catalog.app.bundle.js", "classic_form.app.bundle.js", "Consent.app.bundle.js", "ConstantSum.app.bundle.js", "ContactForm.app.bundle.js",
      "Dashboard.app.bundle.js", "DateTime.app.bundle.js", "Dropdown.app.bundle.js", "EmailInput.app.bundle.js", "employee360_portal.app.bundle.js",
      "employee360_report.app.bundle.js", "eui.app.bundle.js", "FileInput.app.bundle.js", "GroupRank.app.bundle.js", "helpers.app.bundle.js",
      "LabelledOpinionScale.app.bundle.js", "Matrix.app.bundle.js", "Message.app.bundle.js", "MultiChoice.app.bundle.js", "MultiChoicePicture.app.bundle.js",
      "nps_widget.app.bundle.js", "nps-widget-builder.app.bundle.js", "nps.app.bundle.js", "NPSFeedback.app.bundle.js", "NPSScore.app.bundle.js",
      "NumberInput.app.bundle.js", "offline.app.bundle.js", "OpinionScale.app.bundle.js", "PaymentQuestion.app.bundle.js", "PhoneNumber.app.bundle.js",
      "QRCodePrint.app.bundle.js", "QuestionReports.app.bundle.js", "RankOrder.app.bundle.js", "Rating.app.bundle.js", "ResponsesDetailPage.app.bundle.js",
      "Signature.app.bundle.js", "SingleQuestion.app.bundle.js", "Slider.app.bundle.js", "super_admin.app.bundle.js", "SurveyForm.app.bundle.js",
      "TextInput.app.bundle.js", "URLInput.app.bundle.js", "vendors.app.bundle.js", "widget.app.bundle.js","YesNo.app.bundle.js"
    ],
    "ARGOCD_CREDENTIAL_ID": "SurveySparrowArgoCD",
    "CLOUDFLARE": [
      "TOKEN_ID": "CloudflareSecretToken",
      "WEBSITES": [
        "EdithDocs": [
          "repoUrl": "*****************:surveysparrow/emailservice-api-doc.git",
          "folderName": "edithdocs",
          "packageManager": "yarn",
          "buildDir": "build",
          "cloudflareCacheEnabled": false
        ]
      ]
    ],
    "DEFAULT_BUMP_TYPE": "patch",
    "MOBILE": [
      "FASTLANE": [
        "AndroidOffline": [
          "signingKeyCredentialId": "OfflineAndroidSigningKey",
          "androidMarketingSSMPath": "/AndroidOffline/marketingVersion",
          "androidBundleSSMPath": "/AndroidOffline/bundleVersion",
          "slackChannel": "offline-release",
          "repoUrl": "*****************:surveysparrow/surveysparrow-offline-app.git",
          "folderName": "surveysparrow-offline-app"
        ]
      ],
      "IOS_CODE_MAGIC": [
        "IOSOffline": [
          "iosMarketingVersionSSMPath": "/IOSOffline/marketingVersion",
          "iosBundleVersionSSMPath": "/IOSOffline/bundleVersion",
          "codeMagicAppId": "64e470a9533d6e4d6a468ee0",
          "iosDefaultBumpType": "patch",
          "slackChannel": "offline-release"
        ]
      ]
    ],
    "PLATFORMS_CONFIG": [
      "ROLE": "PlatformJenkinsAccess",
      "ARGOCD_CREDENTIAL_ID": "PlatformArgoCDCredentials"   
    ],
    "PLATFORMS_APP_CONFIG": [
      "Edith": [
        "repoUrl": "*****************:surveysparrow/emailservice-backend.git",
        "folderName": "edith",
        "configGroups": ["edith-api-server", "edith-worker"],
        "iamRoleName": "EdithServiceRole",
        "layers": [ 
          "worker": [
            "migrationEnabled": false
          ], 
          "api-server": [
            "configSubPath": "/",
            "migrationEnabled": true
          ] 
        ],
        "dcLayerMap": [
          "US-VIRGINIA": [ "api-server", "worker" ]
        ]
      ],
      "Shield": [
        "repoUrl": "*****************:surveysparrow/shield.git",
        "folderName": "shield",
        "configGroups": ["shield"],
        "iamRoleName": "ShieldServiceRole",
        "layers": [ 
          "application": [
            "configSubPath": "/",
            "migrationTargetLayer": "migration",
            "migrationEnabled": true
          ]
        ]
      ],
      "Blink": [
        "repoUrl": "*****************:surveysparrow/blink.git",
        "folderName": "blink",
        "configGroups": ["blink"],
        "iamRoleName": "BlinkServiceRole",
        "layers": [ 
          "application": [
            "configSubPath": "/",
            "migrationTargetLayer": "migration",
            "migrationEnabled": true
          ]
        ]
      ]
    ]
  ]
}