package org.surveysparrow

import org.global.S3

class SetupUtils {

  def context
  def environment
  def serviceVars = [:]
  def variables = [:]

  SetupUtils(context, serviceVars, variables, environment) {
    this.context     = context
    this.serviceVars = serviceVars
    this.variables   = variables
    this.environment = environment
  }

  void prepareRepo(tag, version, packageJsonHash) {
    String configFilePath = "${this.variables.CONFIG_REPO.folder}/${this.environment}/${this.serviceVars.service}/${context.env.ENVIRONMENT}.json"

    context.dir(this.serviceVars.folderName) {
      context.gitUtils.cloneRepo(tag, this.serviceVars.repoUrl, this.variables.BITBUCKET_CREDENTIAL_ID)
      new S3(context).downloadFile("node_modules/${context.env.JOB_NAME}/${packageJsonHash}/node_modules.tar.gz", this.variables.ARTIFACTS_BUCKET)
      context.sh(
                script: '''
                    mkdir -p log && touch log/server.log
                tar -xzf node_modules.tar.gz --no-same-permissions
                '''
            )
    }

    context.dir(this.variables.CONFIG_REPO.folder) {
      String branch = this.variables.DEFAULT_BRANCH

      if (this.variables.REGION_NAMES.contains(environment)) {
        branch = this.variables.PRODUCTION_BRANCH
      }

      context.gitUtils.cloneRepo(branch, this.variables.CONFIG_REPO.repoUrl, this.variables.BITBUCKET_CREDENTIAL_ID)
    }

    context.sh(script: """
                jq '.version |= \"${version}\"' ${configFilePath} > temp.json
                cp temp.json ${this.serviceVars.folderName}/config/${context.env.ENVIRONMENT}.json
            """
        )

    if (this.serviceVars.service == 'app-v1') {
      context.sh(
                script: """
                    cp ${this.variables.CONFIG_REPO.folder}/${this.environment}/files/cassandra/ca.pem ${this.serviceVars.folderName}/server/external/cassandra/
                    cp ${this.variables.CONFIG_REPO.folder}/${this.environment}/files/cassandra/sf-class2-root.crt ${this.serviceVars.folderName}/server/external/cassandra/
                    cp ${this.variables.CONFIG_REPO.folder}/${this.environment}/files/google/* ${this.serviceVars.folderName}/config/
                    cp ${this.variables.CONFIG_REPO.folder}/${this.environment}/files/sentry/.sentryclirc ${this.serviceVars.folderName}/
                """
            )
        } else { // for microservices - App-v1 SOC
      context.sh(
                script: """
                    mkdir -p ${this.serviceVars.folderName}/db/credentials/cassandra/
                    cp ${this.variables.CONFIG_REPO.folder}/${this.environment}/files/cassandra/ca.pem ${this.serviceVars.folderName}/db/credentials/cassandra/
                    cp ${this.variables.CONFIG_REPO.folder}/${this.environment}/files/cassandra/sf-class2-root.crt ${this.serviceVars.folderName}/db/credentials/cassandra/
                    cp ${this.variables.CONFIG_REPO.folder}/${this.environment}/files/google/* ${this.serviceVars.folderName}/config/
                """
            )
    }
  }

}
