const fs = require('fs');
const path = require('path');
const handlebars = require('handlebars');

class SharedLibraryDocGenerator {
  constructor(libraryRoot) {
    this.libraryRoot = libraryRoot;
    this.outputDir = path.join(process.cwd(), 'docs');
  }

  /**
   * Generates documentation for Jenkins Shared Library
   */
  async generateDocs() {
    fs.mkdirSync(this.outputDir, { recursive: true });

    const documentationTree = {
      Global: [],
      Classes: {}
    };

    // Parse vars directory (typically global methods)
    this.parseDirectory(path.join(this.libraryRoot, 'vars'), documentationTree.Global);

    // Parse src directory (classes and their methods)
    this.parseDirectory(path.join(this.libraryRoot, 'src'), documentationTree, true);

    if (Object.values(documentationTree.Classes).length === 0 && documentationTree.Global.length === 0) {
      console.warn('No methods with documentation found.');
      return;
    }

    this.generateHTML(documentationTree);
    console.log(`Documentation generated at ${this.outputDir}/index.html`);
  }

  /**
   * Recursively parse documentation from files in a directory
   * @param {string} dirPath - Path to directory to parse
   * @param {Object|Array} targetContainer - Container to store parsed methods
   * @param {boolean} [parseClasses=false] - Whether to parse class methods
   * @returns {void}
   */
  parseDirectory(dirPath, targetContainer, parseClasses = false) {
    if (!fs.existsSync(dirPath)) return;

    fs.readdirSync(dirPath).forEach(file => {
      const fullPath = path.join(dirPath, file);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        this.parseDirectory(fullPath, targetContainer, parseClasses);
      } else if (path.extname(file) === '.groovy') {
        const fileDocs = this.parseGroovyFile(fullPath, parseClasses);

        if (fileDocs) {
          if (parseClasses) {
            if (fileDocs.className) {
              if (!targetContainer.Classes[fileDocs.className]) {
                targetContainer.Classes[fileDocs.className] = [];
              }
              targetContainer.Classes[fileDocs.className].push(...fileDocs.methods);
            } else {
              targetContainer.Global.push(...fileDocs.methods);
            }
          } else {
            // For vars directory, add all methods from the file
            targetContainer.push(...fileDocs);
          }
        }
      }
    });
  }

  /**
   * Parse a Groovy file for documentation
   * @param {string} filePath - Path to the Groovy file
   * @param {boolean} parseClasses - Whether to parse class methods
   * @returns {Object|null} Parsed file documentation
   */
  parseGroovyFile(filePath, parseClasses = false) {
    const content = fs.readFileSync(filePath, 'utf8');

    // If not parsing classes, use existing method for vars directory
    if (!parseClasses) {
      const methodDoc = this.parseMethodDocs(filePath);
      return methodDoc ? methodDoc : null;
    }

    // For src directory, parse both class and method documentation
    const methods = [];
    let className = null;

    // Find class declaration
    const classMatch = content.match(/(?:^|\n)\s*class\s+([a-zA-Z_]\w*)/);
    if (classMatch) {
      className = classMatch[1];
    }

    // Find all method documentation
    const methodMatches = content.matchAll(/\/\*\*\s*([\s\S]*?)\*\/\s*(?:(?:public|private|protected|def|String|Int|void|Boolean|Map)\s+)*(?:static\s+)?(?:def\s+)?([a-zA-Z_]\w*)\s*\(/g);

    for (const match of methodMatches) {
      const docContent = match[1];
      const methodName = match[2];

      // Extract description
      const descMatch = docContent.match(/^\s*([^\n@]+)/);
      const description = descMatch ? descMatch[1].trim().replace(/^\*/, '').trim() : 'No description provided.';

      // Extract parameters
      const params = [];
      const paramMatches = docContent.matchAll(/\*\s*@param\s+(\w+)\s+(.+?)(?=\n\s*\*|$)/gs);
      for (const paramMatch of paramMatches) {
        params.push({
          name: paramMatch[1],
          description: paramMatch[2].trim()
        });
      }

      // Extract return value
      const returnMatch = docContent.match(/\*\s*@return\s+([\s\S]*?)(?=\n\s*\*|$)/);
      const returnValue = returnMatch ? returnMatch[1].trim() : null;

      // Extract example
      // const exampleMatch = docContent.match(/\*\s*@example\s*\n([\s\S]*?)(?=\n\s*\*\/|$)/);
      // const example = exampleMatch ? exampleMatch[1].trim().replace(/^\*/, '').trim() : null;
      const exampleMatch = docContent.match(/\*\s*@example\s*\n([\s\S]*?)(?=\n\s*\*\/|$)/);
      const example = exampleMatch
        ? exampleMatch[1]
            .split('\n')
            .map(line => line.replace(/^\s*\*/, '').trim())
            .join('\n')
        : null;


      methods.push({
        name: methodName,
        description,
        params,
        returnValue,
        example,
        fileName: path.basename(filePath)
      });
    }

    return methods.length > 0 ? { className, methods } : null;
  }

  /**
   * Parse JavaDoc-style comments from a vars Groovy file
   * @param {string} filePath - Path to the Groovy file
   * @returns {Object|null} Parsed method documentation
   */
  parseMethodDocs(filePath) {
    const content = fs.readFileSync(filePath, 'utf8');
    const methods = [];
    const fileName = path.basename(filePath, '.groovy');

    // Match all JavaDoc comments followed by method definitions
    const methodPattern = /\/\*\*\s*([\s\S]*?)\*\/\s*(?:(?:public|private|protected|def|String|Int|void|Boolean|Map)\s+)*(?:static\s+)?(?:def\s+)?([a-zA-Z_]\w*)\s*\([^)]*\)/g;
    
    let match;
    while ((match = methodPattern.exec(content)) !== null) {
      const docContent = match[1];
      let methodName = match[2];

      // If the method is 'call', use the filename as the method name
      if (methodName === 'call') {
        methodName = fileName;
      }

      // Extract description
      const descMatch = docContent.match(/^\s*\*?\s*([^\n@]+)/);
      const description = descMatch ? descMatch[1].trim().replace(/^\*/, '').trim() : 'No description provided.';

      // Extract parameters
      const params = [];
      const paramMatches = docContent.matchAll(/\*\s*@param\s+(\w+)\s+(.+?)(?=\n\s*\*|$)/gs);
      for (const paramMatch of paramMatches) {
        params.push({
          name: paramMatch[1],
          description: paramMatch[2].trim()
        });
      }

      // Extract return value
      const returnMatch = docContent.match(/\*\s*@return\s+([\s\S]*?)(?=\n\s*\*|$)/);
      const returnValue = returnMatch ? returnMatch[1].trim() : null;

      // Extract example
      const exampleMatch = docContent.match(/\*\s*@example\s*\n([\s\S]*?)(?=\n\s*\*\/|$)/);
      const example = exampleMatch
        ? exampleMatch[1]
            .split('\n')
            .map(line => line.replace(/^\s*\*/, '').trim())
            .join('\n')
        : null;

      methods.push({
        name: methodName,
        description,
        params,
        returnValue,
        example,
        fileName: path.basename(filePath)
      });
    }

    return methods.length > 0 ? methods : null;
  }

  /**
   * Generate HTML documentation using Handlebars template
   * @param {Object} documentationTree - Parsed documentation tree
   */
  generateHTML(documentationTree) {
    const template = handlebars.compile(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>Jenkins Shared Library Documentation</title>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/default.min.css">
            <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
            <script>hljs.highlightAll();</script>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 20px; }
                .section { margin-bottom: 30px; }
                .toc { margin-bottom: 30px; }
                .toc ul { list-style: none; padding: 0; }
                .toc li { margin: 5px 0; }
                .method, .class { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; background: #f9f9f9; }
                .class { background: #e0f7fa; }
                .method-name { font-weight: bold; font-size: 1.2em; }
                .method-file { font-size: 0.9em; color: #666; }
                .description { margin: 10px 0; color: #444; }
                .params ul { list-style: none; padding: 0; }
                .params li { margin: 5px 0; }
                .return { margin: 10px 0; color: #333; }
                .example pre { background: #eee; padding: 10px; font-family: monospace; border-left: 3px solid #999; }
                #search { margin-bottom: 20px; padding: 10px; width: 100%; border: 1px solid #ddd; }
                h2 { border-bottom: 2px solid #333; padding-bottom: 10px; }
            </style>
            <script>
                function filterMethods() {
                    const query = document.getElementById('search').value.toLowerCase();
                    document.querySelectorAll('.method').forEach(method => {
                        const text = method.textContent.toLowerCase();
                        method.style.display = text.includes(query) ? 'block' : 'none';
                    });
                }
            </script>
        </head>
        <body>
            <h1>Jenkins Shared Library Documentation</h1>
            <input type="text" id="search" placeholder="Search..." onkeyup="filterMethods()">

            <div class="toc">
                <h2>Table of Contents</h2>
                {{#if Global.length}}
                <h3>Global Methods</h3>
                <ul>
                    {{#each Global}}
                    <li><a href="#{{name}}">{{name}}</a></li>
                    {{/each}}
                </ul>
                {{/if}}

                {{#if Classes}}
                <h3>Classes</h3>
                <ul>
                    {{#each Classes}}
                    <li><strong>{{@key}}</strong>
                        <ul>
                            {{#each this}}
                            <li><a href="#{{name}}">{{name}}</a></li>
                            {{/each}}
                        </ul>
                    </li>
                    {{/each}}
                </ul>
                {{/if}}
            </div>

            {{#if Global.length}}
            <div class="section">
                <h2>Global Methods</h2>
                {{#each Global}}
                <div class="method" id="{{name}}">
                    <div class="method-name">{{name}}</div>
                    <div class="method-file">File: {{fileName}}</div>
                    <div class="description">{{description}}</div>
                    {{#if params.length}}
                    <div class="params">
                        <strong>Parameters:</strong>
                        <ul>
                            {{#each params}}
                            <li><strong>{{name}}:</strong> {{description}}</li>
                            {{/each}}
                        </ul>
                    </div>
                    {{/if}}
                    {{#if returnValue}}
                    <div class="return">
                        <strong>Returns:</strong> {{returnValue}}
                    </div>
                    {{/if}}
                    {{#if example}}
                    <div class="example">
                        <strong>Example:</strong>
                        <pre><code class="groovy">{{example}}</code></pre>
                    </div>
                    {{/if}}
                </div>
                {{/each}}
            </div>
            {{/if}}
            
            {{#if Classes}}
            <div class="section">
                <h2>Classes</h2>
                {{#each Classes}}
                <div class="section">
                    <h3>{{@key}}</h3>
                    {{#each this}}
                    <div class="method" id="{{name}}">
                        <div class="method-name">{{name}}</div>
                        <div class="method-file">File: {{fileName}}</div>
                        <div class="description">{{description}}</div>
                        {{#if params.length}}
                        <div class="params">
                            <strong>Parameters:</strong>
                            <ul>
                                {{#each params}}
                                <li><strong>{{name}}:</strong> {{description}}</li>
                                {{/each}}
                            </ul>
                        </div>
                        {{/if}}
                        {{#if returnValue}}
                        <div class="return">
                            <strong>Returns:</strong> {{returnValue}}
                        </div>
                        {{/if}}
                        {{#if example}}
                        <div class="example">
                            <strong>Example:</strong>
                            <pre><code class="groovy">{{example}}</code></pre>
                        </div>
                        {{/if}}
                    </div>
                    {{/each}}
                </div>
                {{/each}}
            </div>
            {{/if}}
        </body>
        </html>
        `);

    const html = template(documentationTree);
    fs.writeFileSync(path.join(this.outputDir, 'index.html'), html);
  }

}

// CLI support
if (require.main === module) {
  const libraryRoot = process.argv[2];
  if (!libraryRoot) {
    console.error('Usage: node documentGenerator.js /path/to/jenkins/shared/library');
    process.exit(1);
  }
  const generator = new SharedLibraryDocGenerator(libraryRoot);
  generator.generateDocs();
}

module.exports = SharedLibraryDocGenerator;