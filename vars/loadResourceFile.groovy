/**
 * Load a resourcefile from resources/ directory of shared libraries. It writes the file in the current
 * directory with the name of the file from the resources directory.
 *
 * @param resourcePath (String) The path of the resource file in resources/ directory of shared libraries.
 *
 * @example
 * loadResourceFile('global/jirahelper.py')
 */
def call(String resourcePath) {
  def resourceFileContent = libraryResource(resourcePath)
  def fileName = resourcePath.split('/')[-1]  

  writeFile file: fileName, text: resourceFileContent
}