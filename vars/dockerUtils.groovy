import org.global.CloudAuthManager

def cloudAuthManagerObject = null;

/**
 * Docker Build Utility for Jenkins Pipelines
 * 
 * @param dockerfile (String) Path to the Dockerfile. Default: 'Dockerfile'.
 * @param repo (String) The docker repository to tag the image with.
 * @param tags (List<String>) List of tags to apply to the image. Default: [].
 * @param buildArgs (Map<String, String>) Map of build arguments to pass. Default: [:].
 * @param additionalArgs (List<String>) Additional Docker build flags. Default: [].
 */
def build(String dockerfile = "./Dockerfile", String repo, List<String> tags = [ 'latest' ], Map<String, String> buildArgs = [:], List<String> additionalArgs = []) {
  String command = "DOCKER_BUILDKIT=1 docker build -f ${dockerfile}"

  tags.each { tag ->
    command += " -t ${repo}:${tag}"
  }

  buildArgs.each { key, value ->
    command += " --build-arg ${key}=${value}"
  }

  additionalArgs.each { arg ->
    command += " ${arg}"
  }

  command += " ."

  sh command
}

/**
 * Push the docker image to the docker repository
 *
 * @param repo (String) the docker repository to push the image
 * @param tags (List<String>) the tags of the image
 */
def push(String repo, List<String> tags = [ 'latest' ]) {
  tags.each { tag ->
    sh "docker push ${repo}:${tag}"
  }
}

/**
 * Authenticate to docker registry
 *
 * @param registry (String) the docker registry url (ECR, Dockerhub)
 * @param region (String) the region where the docker registry resides
 * @param config (Map) the config for cross account authentication
 */
def authenticate(registry, region, config = [:]) { 
  cloudAuthManagerObject = new CloudAuthManager(this, region, config)
  cloudAuthManagerObject.authenticateToContainerRegistry(registry)
}

/**
 * Clear the set authentication credentials incase of cross account authentication
 */
def clearAuth() {
  if (cloudAuthManagerObject) {
    cloudAuthManagerObject.clearCredentials()
  }
}

/**
 * Prune all the docker images
 */
def pruneAllImages() {
  sh "docker image prune --all --force"
}
