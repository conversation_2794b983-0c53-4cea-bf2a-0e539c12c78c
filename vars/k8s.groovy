import org.global.CloudAuthManager

def cloudAuthManagerObject = null;

/**
 * Check if a deployment with a given name exists in a give namespace. Internal method, to use separately, must authenticate to CloudProvider
 *
 * @param deploymentName (String) the name of the deployment to check
 * @param namespace (String) the namespace to check if the deployment exists
 */
def checkIfDeploymentExists(String deploymentName, String namespace) {
  def deploymentExists = sh(script: "kubectl get deployment ${deploymentName} -n ${namespace}", returnStatus: true)
  return !deploymentExists
}

/*
 * Wait for a rollout to complete
 *
 * @param deploymentName (String) the name of the deployment to wait
 * @param namespace (String) the namespace where the deployment resides
 */
def rolloutStatus(String deploymentName, String namespace) {
  sh "kubectl rollout status deployment ${deploymentName} -n ${namespace}"
}

/**
 * Restart a given deployment in a given namespace. Cloud authentication is handled within the method.
 *
 * @param deploymentName (String) the name of the deployment to restart
 * @param namespace (String) the namespace in which the deployment exists
 */
def restartDeployment(String deploymentName, String namespace) {
  if(checkIfDeploymentExists(deploymentName, namespace)) {
    sh "kubectl rollout restart deployment ${deploymentName} -n ${namespace}"
    rolloutStatus(deploymentName, namespace)
  }
}

/**
 * Apply a give yaml file from the specified path
 * 
 * @param filePath (String) the path where the YAML file resides
 * @param kustomize (Boolean) if the apply is kustomize. (Default = false)
 */
def apply(String filePath, Boolean kustomize = false) {
  if (fileExists(filePath)) {
    sh "kubectl apply ${kustomize ? '-k' : '-f'} ${filePath}"
  } else {
    error "[-] File ${filePath} not found"
  }
}

/**
 * Create a resource from the given yaml file from the specified path, this can be used for Jobs
 * 
 * @param filePath (String) the path where the YAML file resides
 * @param kustomize (Boolean) if the apply is kustomize. (Default = false)
 */
def create(String filePath, Boolean kustomize = false) {
  if (fileExists(filePath)) {
    sh "kubectl create ${kustomize ? '-k' : '-f'} ${filePath}"
  } else {
    error "[-] File ${filePath} not found"
  }
}

/**
 * Wait for a condition to be satisfied
 *
 * @param condition (String) the condition to wait for
 * @param resource (String) the type of resource to wait on
 * @param selector (Map) the label selector to identify the resource
 * @param namespace (String) the namespace in which the resource resides
 * @param timeout (int) the timeout in seconds (Default = 15)
*/
def waitForCondition(String condition, String resource, Map labels, String namespace, int timeout = 8) {
  def labelSelector = labelBuilderFromMap(labels)
  sh "kubectl wait --for=condition=${condition} ${resource} --selector ${labelSelector} --namespace ${namespace} --timeout ${timeout}m"
}

/**
  * Wait for a job to complete based on labels
  *
  * @param labels (Map) the labels in a key:value format, this will perform an AND label operation.
  * meaning, all labels will be matched to find the job.
  * @param namespace (String) the namespace in which the job resides.
  * @param timeout (int) the timeout in minutes (Default = -1) which means wait for one week.
*/
def waitUntilJobCompletion(Map labels, String namespace, timeout=-1) {
  def labelSelector = labelBuilderFromMap(labels)
  sh "kubectl wait --for=jsonpath='{.status.conditions[0].status}' job -l ${labelSelector} -n ${namespace} --timeout=${timeout}m"
}

/**
 * Authenticate to a Kubernetes cluster
 *
 * @param clusterName (String) the name of the Kubernetes cluster
 * @param region (String) the region in which the kubernetes cluster resides
 * @param config (Map) Optional - Cross account config if kubernetes cluster is in another account.
 */
def authenticate(String clusterName, String region, Map config = [:]) {
  cloudAuthManagerObject = new CloudAuthManager(this, region, config)
  cloudAuthManagerObject.authenticateToK8sCluster(clusterName)
}

/**
 * Check if a Kubernetes job has succeeded using labels
 *
 * @param selector (Map) the label selector to identify the job
 * @param namespace (String) the namespace in which the job resides
 * @return boolean indicating if the job has succeeded
 */
def hasJobSucceededByLabels(Map labels, String namespace) {
  def labelSelector = labelBuilderFromMap(labels)
  def result = sh(
    script: "kubectl get jobs --selector ${labelSelector} --namespace ${namespace} --output json | jq '.items[].status.succeeded | select(.==1)' -e", 
    returnStatus: true
  )
  return result == 0
}

/**
 * Check if a Kubernetes job has succeeded
 *
 * @param jobName (String) the name of the job
 * @param namespace (String) the namespace in which the job resides
 * @return boolean indicating if the job has succeeded
 */
def hasJobSucceededByName(String jobName, String namespace) {
  def result = sh(
    script: "kubectl get job ${jobName} --namespace ${namespace} --output json | jq '.status.succeeded | select(.==1)' -e", 
    returnStatus: true
  )
  return result == 0
}

/**
 * Clear the authentication credentials if authenticated across accounts
 */
def clearAuth() {
  cloudAuthManagerObject.clearCredentials()
}

/**
 * Delete a kubernetes object in the specified namespace
 *
 * @param resource (String) the kind of the resource.
 * @param resourceName (String) the name of the resource.
 * @param namespace (String) the namespace where the resource resides.
 */
def delete(String resource, String resourceName, String namespace) {
  sh "kubectl delete ${resource} ${resourceName} --namespace ${namespace}"
}

/**
  * Watch logs of a pod based on labels
  *
  * @param labels (Map) the labels in a key:value format, this will perform an AND label operation.
  * meaning, all labels will be matched to find the pod.
  * @param namespace (String) the namespace in which the pod resides.
*/  
def watchLogs(Map labels, String namespace) {
  def labelSelector = labelBuilderFromMap(labels)
  sh "kubectl logs --follow -l ${labelSelector} -n ${namespace}"
}

/**
 * Get logs of a pod based on labels
 *
 * @param labels (Map) the labels in a key:value format, this will perform an AND label operation.
 * meaning, all labels will be matched to find the pod.
 * @param namespace (String) the namespace in which the pod resides.
 * @param filePath (String) the path to write the logs to
 */
def logToFile(Map labels, String namespace, String filePath = "./log.txt") {
  def labelSelector = labelBuilderFromMap(labels)
  sh "kubectl logs --selector ${labelSelector} --namespace ${namespace} --tail=-1 > ${filePath}"
}


/**
  * Print live logs of a pod based on labels. This method will wait for the pod to be ready and then print the logs.
  * The Wait for job completion will run in parallel to act as panic handler incase of any issue in live logs.
  *
  * @param selector (String) the label selector to identify the pod
  * @param namespace (String) the namespace in which the pod resides
  * @param readinessTimeout (int) the timeout in seconds to wait for the pod to be ready
  * @param activeDeadline (int) the timeout in seconds for the job to complete
 */
def liveLogs(Map labelSelector, String namespace, int readinessTimeout, int activeDeadline = -1) {
  def logsPrintStarted = false
  parallel (
    "live logs": {
      try {
        waitForCondition('ready', 'pod', labelSelector, namespace, readinessTimeout?: 5)
        logsPrintStarted = true
        watchLogs(labelSelector, namespace)
      } catch (Exception e) {
        logsPrintStarted = false
        def pendingPods = getPendingPodsCountByLabels(labelSelector, namespace)
        if (pendingPods > 0) {
          deleteJob(labelSelector, namespace)
          error "[-] Pod is under pending state"
        }
        echo "[-] Pod is not catched during readiness timeout of ${readinessTimeout} seconds or failed to get logs"
      }
    },
    "Wait for job completion": {
      waitUntilJobCompletion(labelSelector, namespace, activeDeadline ?: -1)
      if (!logsPrintStarted) {
        tempFile = sh (script: "mktemp", returnStdout: true).trim()
        logToFile(labelSelector, namespace, tempFile)
        sh "cat ${tempFile}"
      }
    },
    failFast: true
  )
}

/**
 * Get the count of pending pods based on labels
 *
 * @param labels (Map) the labels in a key:value format
 * @param namespace (String) the namespace in which the pods reside
 * @return (int) the count of pending pods
 */
def getPendingPodsCountByLabels(Map labels, String namespace) {
  def labelSelector = labelBuilderFromMap(labels)
  def pendingPods = sh (
    script: "kubectl get pods -l ${labelSelector} -n ${namespace} --output json | jq '.items[].status.phase' | grep -c -w 'Pending'", 
    returnStdout: true
  ).trim()

  return pendingPods.toInteger()
}

/**
 * Build a label selector from a map of labels
 *
 * @param labels (Map) the labels in a key:value format
 * @return (String) the label selector
 */
def labelBuilderFromMap(Map labels) {
  def labelSelector = ""
  labels.each { key, value ->
    if (labelSelector.length() > 0) {
      labelSelector += ","
    }

    labelSelector += "${key}=${value}"
  }
  return labelSelector
}

/**
 * Delete a job based on labels
 *
 * @param labels (Map) the labels in a key:value format
 * @param namespace (String) the namespace in which the job resides
 */
def deleteJob(Map labels, String namespace) {
  def labelSelector = labelBuilderFromMap(labels)
  sh "kubectl delete job -l ${labelSelector} -n ${namespace}"
}

/*
 * Update image of a kubernetes deployment. Writing this as a specific method, because there is no direct way to update
 * resources without knowing the specifics and this image update is more frequent.
 *
 * @param deployment (String) the name of the deployment to edit
 * @param container (String) the name of the container to update the image
 * @param image (String) the image url to update
 * @param namespace (String) the namespace in which the resource resides
 */
def editDeploymentImage(String deployment, String container, String image, String namespace) {
  sh "kubectl set image deployment/${deployment} ${container}=${image} -n ${namespace}"
}

/**
 * Wait for a rollout to complete based on kubernetes labels
 *
 * @param labelSelector (String) the label to select the deployment.
 * @param namespace (String) the namespace in which the deployment resides.
 */
def rolloutStatusByLabel(String labelSelector, String namespace) {
  sh "kubectl rollout status deployment -l ${labelSelector} -n ${namespace}"
}

/**
 * Restart a kubernetes Deployment by a given label or labels.
 *
 * @param labels (Map) the labels in a key:value format, this will perform an AND label operation.
 * meaning, all labels will be matched to find the deployment.
 * @param namespace (String) the namespace in which the deployment resides.
 */
def restartByLabel(Map<String, String> labels = [:], String namespace) {
  def labelSelector = labelBuilderFromMap(labels)
  sh "kubectl rollout restart deployment -l ${labelSelector} -n ${namespace}"
  rolloutStatusByLabel(labelSelector, namespace)
}

/**
 * Patch a kubernetes resource
 *
 * @param resource (String) the kind of the resource
 * @param name (String) the name of the resource
 * @param patch (String) the patch to apply to the resource
 * @param namespace (String) the namespace in which the resource resides
 */
def patchResource(String resource, String name, String patch, String namespace) {
  sh "kubectl patch ${resource} ${name} -p '${patch}' -n ${namespace}"
}
