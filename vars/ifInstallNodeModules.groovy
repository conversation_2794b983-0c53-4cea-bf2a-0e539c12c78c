import org.global.S3

/**
 * Check whether to install node_modules or not. Given the referencefile, S3 bucket and path.
 * This method generates md5sum of the reference file and checks whether there is a file or folder in S3 bucket in 
 * the specified hash path. If the file is present returns the md5hash, If it is not present npm install is run and
 * tar.gz file is uploaded to S3 on the md5hash path.
 *
 * @param referenceFile (String) The file to watch the changes for, get the md5sum for (eg. package.json, package-lock.json)
 * @param bucket (String) The S3 bucket to check and upload the node_modules directory
 * @param path (String) The path where to upload or download the node_modules from
 *
 * @return md5Hash (String) The md5 hash of the reference file so you can pull the tar file wherever in the pipeline
 */
def call(referenceFile, bucket, path) {
  if (!path.endsWith('/')) {
    path += "/"
  }

  def s3Client  = new S3(this);
  def md5Hash   = sh (script: "md5sum ${referenceFile} | tr -d '\n' | cut -d' ' -f1 | tr -d '\n'", returnStdout: true)
  def fileExists = s3Client.fileOrFolderExists(md5Hash, path, bucket)

  if (fileExists) {
    s3Client.downloadFile("${path}${md5Hash}/node_modules.tar.gz", bucket)
    sh """
      mkdir -p log && touch log/server.log
      tar -xzf node_modules.tar.gz --no-same-permissions
    """
  } else {
    sh """
      rm -rf node_modules
      npm cache clean --force
      npm config set strict-ssl=false
      npm install
      tar -czf node_modules.tar.gz node_modules
    """

    s3Client.uploadFile('node_modules.tar.gz', bucket, path + md5Hash + "/node_modules.tar.gz")
  }

  sh "rm -rf node_modules.tar.gz"
  return md5Hash
}