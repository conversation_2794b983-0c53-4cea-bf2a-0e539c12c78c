import org.global.EcsManager
import org.global.ParameterValidationUtil
import org.global.CloudAuthManager

/**
 * Deploy containerized applications to AWS ECS services with support for cross-account deployments,
 * multiple deployment strategies, and comprehensive monitoring.
 *
 * This function serves as the main entry point for ECS deployments and follows the established
 * patterns from the existing <PERSON> shared library.
 *
 * @param config (Map) Configuration map containing deployment parameters
 *
 * Required Parameters:
 * @param serviceName (String) MANDATORY The name of the ECS service to deploy
 * @param clusterName (String) MANDATORY The name of the ECS cluster
 * @param taskDefinitionFamily (String) MANDATORY The task definition family name
 * @param containerImage (String) MANDATORY The container image URI (e.g., ECR image)
 * @param region (String) MANDATORY The AWS region for deployment
 *
 * Optional Parameters:
 * @param action (String) The deployment action: 'DEPLOY', 'UPDATE', 'ROLLBACK', 'SCALE'. Default: 'DEPLOY'
 * @param deploymentStrategy (String) Deployment strategy: 'ROLLING', 'BLUE_GREEN'. Default: 'ROLLING'
 * @param desiredCount (Integer) Number of desired tasks. Default: 1
 * @param cpu (String) Task CPU allocation (e.g., '256', '512'). Default: '256'
 * @param memory (String) Task memory allocation (e.g., '512', '1024'). Default: '512'
 * @param networkMode (String) Network mode: 'awsvpc', 'bridge', 'host'. Default: 'awsvpc'
 * @param requiresCompatibilities (List) Launch types: ['FARGATE', 'EC2']. Default: ['FARGATE']
 * @param subnets (List) List of subnet IDs for awsvpc network mode
 * @param securityGroups (List) List of security group IDs for awsvpc network mode
 * @param assignPublicIp (String) Assign public IP: 'ENABLED', 'DISABLED'. Default: 'DISABLED'
 * @param environmentVariables (Map) Environment variables for the container
 * @param secrets (Map) Secrets from AWS Systems Manager Parameter Store or Secrets Manager
 * @param healthCheckGracePeriodSeconds (Integer) Health check grace period. Default: 300
 * @param maxHealthyPercent (Integer) Maximum healthy percent during deployment. Default: 200
 * @param minHealthyPercent (Integer) Minimum healthy percent during deployment. Default: 50
 * @param enableLogging (Boolean) Enable CloudWatch logging. Default: true
 * @param logGroupName (String) CloudWatch log group name. Default: "/ecs/{serviceName}"
 * @param logRetentionDays (Integer) Log retention in days. Default: 7
 * @param executionRoleArn (String) Task execution role ARN
 * @param taskRoleArn (String) Task role ARN
 * @param accountConfig (Map) Cross-account configuration for IAM role assumption
 * @param waitForDeployment (Boolean) Wait for deployment to complete. Default: true
 * @param deploymentTimeoutMinutes (Integer) Deployment timeout in minutes. Default: 15
 * @param enableRollback (Boolean) Enable automatic rollback on failure. Default: true
 * @param notificationConfig (Map) Configuration for deployment notifications
 * @param tags (Map) Tags to apply to ECS resources
 *
 * Cross-Account Configuration:
 * @param accountConfig.crossAccount (Boolean) Enable cross-account deployment
 * @param accountConfig.roleArn (String) IAM role ARN for cross-account access
 * @param accountConfig.accountId (String) Target AWS account ID
 * @param accountConfig.duration (Integer) Session duration in seconds. Default: 900
 *
 * Notification Configuration:
 * @param notificationConfig.enabled (Boolean) Enable notifications. Default: false
 * @param notificationConfig.channel (String) Notification channel/webhook
 * @param notificationConfig.onSuccess (Boolean) Notify on success. Default: true
 * @param notificationConfig.onFailure (Boolean) Notify on failure. Default: true
 *
 * @example Basic ECS deployment:
 * ecsDeployment([
 *   serviceName: 'my-api-service',
 *   clusterName: 'production-cluster',
 *   taskDefinitionFamily: 'my-api-task',
 *   containerImage: '************.dkr.ecr.us-east-1.amazonaws.com/my-api:latest',
 *   region: 'us-east-1',
 *   desiredCount: 3,
 *   subnets: ['subnet-12345', 'subnet-67890'],
 *   securityGroups: ['sg-abcdef']
 * ])
 *
 * @example Cross-account deployment with custom configuration:
 * ecsDeployment([
 *   serviceName: 'my-api-service',
 *   clusterName: 'production-cluster',
 *   taskDefinitionFamily: 'my-api-task',
 *   containerImage: '************.dkr.ecr.us-east-1.amazonaws.com/my-api:v1.2.3',
 *   region: 'us-east-1',
 *   action: 'UPDATE',
 *   deploymentStrategy: 'BLUE_GREEN',
 *   desiredCount: 5,
 *   cpu: '512',
 *   memory: '1024',
 *   subnets: ['subnet-12345', 'subnet-67890'],
 *   securityGroups: ['sg-abcdef'],
 *   environmentVariables: [
 *     'ENV': 'production',
 *     'LOG_LEVEL': 'info'
 *   ],
 *   accountConfig: [
 *     crossAccount: true,
 *     roleArn: 'arn:aws:iam::************:role/ECSDeploymentRole',
 *     accountId: '************'
 *   ],
 *   notificationConfig: [
 *     enabled: true,
 *     channel: '#deployments',
 *     onSuccess: true,
 *     onFailure: true
 *   ]
 * ])
 */
def call(Map config = [:]) {
  def startTime = System.currentTimeMillis()
  
  try {
    echo "[-] Starting ECS deployment for service: ${config.serviceName}"
    
    // Validate required parameters
    validateParameters(config)
    
    // Set default values
    config = setDefaults(config)
    
    // Initialize ECS manager with authentication
    def ecsManager = initializeEcsManager(config)
    
    // Send deployment started notification
    sendNotification(config, 'DEPLOYMENT_STARTED', null)
    
    // Execute deployment based on action
    def deploymentResult = executeDeployment(ecsManager, config)
    
    // Calculate deployment duration
    def duration = (System.currentTimeMillis() - startTime) / 1000
    
    echo "[-] ECS deployment completed successfully in ${duration} seconds"
    echo "[-] Service: ${config.serviceName}"
    echo "[-] Cluster: ${config.clusterName}"
    echo "[-] Task Definition: ${deploymentResult.taskDefinitionArn}"
    echo "[-] Deployment ID: ${deploymentResult.deploymentId}"
    
    // Send success notification
    sendNotification(config, 'DEPLOYMENT_SUCCESSFUL', deploymentResult)
    
    return deploymentResult
    
  } catch (Exception e) {
    def duration = (System.currentTimeMillis() - startTime) / 1000
    echo "[-] ECS deployment failed after ${duration} seconds"
    echo "[-] Error: ${e.getMessage()}"
    
    // Send failure notification
    sendNotification(config, 'DEPLOYMENT_FAILED', [error: e.getMessage()])
    
    // Attempt rollback if enabled
    if (config.enableRollback && config.action != 'ROLLBACK') {
      echo "[-] Attempting automatic rollback..."
      try {
        def rollbackConfig = config.clone()
        rollbackConfig.action = 'ROLLBACK'
        rollbackConfig.enableRollback = false // Prevent infinite rollback loop
        call(rollbackConfig)
      } catch (Exception rollbackError) {
        echo "[-] Rollback failed: ${rollbackError.getMessage()}"
      }
    }
    
    throw e
  }
}

/**
 * Validate required parameters and parameter combinations
 */
def validateParameters(Map config) {
  def requiredParams = [
    'serviceName', 'clusterName', 'taskDefinitionFamily', 
    'containerImage', 'region'
  ]
  
  ParameterValidationUtil.validateParameterMap(requiredParams, config, requiredParams)
  
  // Validate network configuration for awsvpc mode
  if (config.networkMode == 'awsvpc' || !config.networkMode) {
    if (!config.subnets || config.subnets.isEmpty()) {
      error("[-] Subnets are required for awsvpc network mode")
    }
    if (!config.securityGroups || config.securityGroups.isEmpty()) {
      error("[-] Security groups are required for awsvpc network mode")
    }
  }
  
  // Validate deployment strategy
  def validStrategies = ['ROLLING', 'BLUE_GREEN']
  if (config.deploymentStrategy && !validStrategies.contains(config.deploymentStrategy)) {
    error("[-] Invalid deployment strategy. Valid options: ${validStrategies}")
  }
  
  // Validate action
  def validActions = ['DEPLOY', 'UPDATE', 'ROLLBACK', 'SCALE']
  if (config.action && !validActions.contains(config.action)) {
    error("[-] Invalid action. Valid options: ${validActions}")
  }
  
  echo "[-] Parameter validation completed successfully"
}

/**
 * Set default values for optional parameters
 */
def setDefaults(Map config) {
  def defaults = [
    action: 'DEPLOY',
    deploymentStrategy: 'ROLLING',
    desiredCount: 1,
    cpu: '256',
    memory: '512',
    networkMode: 'awsvpc',
    requiresCompatibilities: ['FARGATE'],
    assignPublicIp: 'DISABLED',
    environmentVariables: [:],
    secrets: [:],
    healthCheckGracePeriodSeconds: 300,
    maxHealthyPercent: 200,
    minHealthyPercent: 50,
    enableLogging: true,
    logRetentionDays: 7,
    waitForDeployment: true,
    deploymentTimeoutMinutes: 15,
    enableRollback: true,
    tags: [:]
  ]
  
  defaults.each { key, value ->
    if (!config.containsKey(key)) {
      config[key] = value
    }
  }
  
  // Set default log group name if not provided
  if (!config.logGroupName) {
    config.logGroupName = "/ecs/${config.serviceName}"
  }
  
  // Set default notification config
  if (!config.notificationConfig) {
    config.notificationConfig = [
      enabled: false,
      onSuccess: true,
      onFailure: true
    ]
  }
  
  return config
}

/**
 * Initialize ECS manager with proper authentication
 */
def initializeEcsManager(Map config) {
  echo "[-] Initializing ECS manager with authentication"
  
  def cloudAuthManager = new CloudAuthManager(this, config.region, config.accountConfig ?: [:])
  def ecsManager = new EcsManager(this, config.region, cloudAuthManager)
  
  // Authenticate to AWS
  cloudAuthManager.authenticateToAWS()
  
  echo "[-] ECS manager initialized successfully"
  return ecsManager
}

/**
 * Execute deployment based on the specified action
 */
def executeDeployment(ecsManager, Map config) {
  switch (config.action) {
    case 'DEPLOY':
      return ecsManager.deployService(config)
    case 'UPDATE':
      return ecsManager.updateService(config)
    case 'ROLLBACK':
      return ecsManager.rollbackService(config)
    case 'SCALE':
      return ecsManager.scaleService(config)
    default:
      error("[-] Unknown action: ${config.action}")
  }
}

/**
 * Send deployment notifications if configured
 */
def sendNotification(Map config, String messageType, Map deploymentResult) {
  if (!config.notificationConfig?.enabled) {
    return
  }
  
  def shouldNotify = false
  switch (messageType) {
    case 'DEPLOYMENT_STARTED':
      shouldNotify = true
      break
    case 'DEPLOYMENT_SUCCESSFUL':
      shouldNotify = config.notificationConfig.onSuccess
      break
    case 'DEPLOYMENT_FAILED':
      shouldNotify = config.notificationConfig.onFailure
      break
  }
  
  if (shouldNotify && config.notificationConfig.channel) {
    try {
      // Use existing sendProgressMessage function if available
      if (this.metaClass.respondsTo(this, 'sendProgressMessage')) {
        sendProgressMessage([
          messageType: messageType,
          application: config.serviceName,
          dc: config.region,
          channel: config.notificationConfig.channel,
          deploymentResult: deploymentResult
        ])
      } else {
        echo "[-] Notification: ${messageType} for ${config.serviceName} in ${config.region}"
      }
    } catch (Exception e) {
      echo "[-] Failed to send notification: ${e.getMessage()}"
    }
  }
}
