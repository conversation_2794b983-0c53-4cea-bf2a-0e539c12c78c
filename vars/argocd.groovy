import org.global.ArgoCd

/**
 * Pipeline step to interact with ArgoCD, supported actions - Deploy, ConfigUpdate and Restart
 *
 * @param config (Map) - This is split into the below params, use it inside the map (varies based on action)
 * @param action (String) MANDATORY valid values - 'DEPLOY', 'CONFIG_UPDATE', 'RESTART'
 * @param argoCdUsername (String) MANDATORY ArgoCD Username to authenticate
 * @param argoCdPassword (String) MANDATORY ArgoCD password for the user
 * @param argoCdAppName (Map) MANDATORY ArgoCD appname to work with
 * @param argoCdDomain (String) MANDATORY ArgoCD domain where the server is hosted
 *
 * @param configMapName (String) MANDATORY when action is CONFIG_UPDATE The name of configmap to update.
 * @param deployments (List<String>) MANDATORY when action is CONFIG_UPDATE / RESTART The name of deplyoments to restart.
 *
 * @example
 * argocd action: 'DEPLOY', argoCdUsername: 'platform-argocd', argoCdPassword: 'abcd1234', argoCdAppName: 'blink-us', argoCdDomain: 'argocd.surveysparrow.com'
 * argocd action: 'RESTART', argoCdUsername: 'platform-argocd', argoCdPassword: 'abcd1234', argoCdAppName: 'blink-us', argoCdDomain: 'argocd.surveysparrow.com', deployments: ['blink', 'blink-resync']
 */
def call(Map config = [:]) {
	def validOpts = [ 'argoCdUsername', 'argoCdPassword', 'argoCdAppName', 'argoCdDomain', 'action' ]

	switch (config.action) {
		case 'DEPLOY':
			deploy(config)
			break
		case 'CONFIG_UPDATE':
			configUpdate(config)
			break
		case 'RESTART':
			restart(config)
			break
		case 'SYNC_KUSTOMIZE':
			syncKustomizeApp(config)
			break
		case 'KUSTOMIZE_DEPLOY':
			syncKustomizeApp(config)
			break
		case 'KUSTOMIZE_CONFIG_UPDATE':
			updateKustomizeApp(config)
			break
		default:
			error "[-] Unknown option specified for action, valid actions are ['DEPLOY', 'CONFIG_UPDATE', 'RESTART']"
	}
}

/**
 * Deploy an application through ArgoCD. Logs in to argocd, syncs the given app, logs out of argocd
 *
 * @param config (Map) - This is split into the below params.
 * @param argoCdUsername (String) MANDATORY ArgoCD Username to authenticate
 * @param argoCdPassword (String) MANDATORY ArgoCD password for the user
 * @param argoCdAppName (Map) MANDATORY ArgoCD appname to work with
 * @param argoCdDomain (String) MANDATORY ArgoCD domain where the server is hosted
 */
def deploy(Map config = [:]) {
	def argoCd = new ArgoCd(this, config.argoCdDomain, config.argoCdAppName)

	argoCd.login(config.argoCdUsername, config.argoCdPassword)
	if (config.clearCache) {
		argoCd.refreshAndSync()
	} else {
		argoCd.sync()
	}
	argoCd.logout()

	echo '[+] Deploy called successfully'
}

/**
 * Update the configmap of application through ArgoCD. Logs in to argocd, updates configmap of the given app, logs out of argocd
 *
 * @param config (Map) - This is split into the below params.
 * @param argoCdUsername (String) MANDATORY ArgoCD Username to authenticate
 * @param argoCdPassword (String) MANDATORY ArgoCD password for the user
 * @param argoCdAppName (Map) MANDATORY ArgoCD appname to work with
 * @param argoCdDomain (String) MANDATORY ArgoCD domain where the server is hosted
 * @param configMapName (String) MANDATORY The name of configmap to update.
 * @param deployments (List<String>) MANDATORY The name of deplyoments to restart.
 */
def configUpdate(Map config = [:]) {
	def argoCd        = new ArgoCd(this, config.argoCdDomain, config.argoCdAppName)
	def configResource = ":ConfigMap:${config.configMapName}"

	argoCd.login(config.argoCdUsername, config.argoCdPassword)
	argoCd.sync(configResource)

	for (def deployment in config.deployments) {
		argoCd.run('restart', 'Deployment', deployment)
	}

	argoCd.logout()
}

/**
 * Restart an application through ArgoCD. Logs in to argocd, restarts given deployments, logs out of argocd
 *
 * @param config (Map) - This is split into the below params.
 * @param argoCdUsername (String) MANDATORY ArgoCD Username to authenticate
 * @param argoCdPassword (String) MANDATORY ArgoCD password for the user
 * @param argoCdAppName (Map) MANDATORY ArgoCD appname to work with
 * @param argoCdDomain (String) MANDATORY ArgoCD domain where the server is hosted
 * @param deployments (List<String>) MANDATORY The name of deplyoments to restart.
 */
def restart(Map config = [:]) {
	def argoCd = new ArgoCd(this, config.argoCdDomain, config.argoCdAppName)

	argoCd.login(config.argoCdUsername, config.argoCdPassword)
	for (def deployment in config.deployments) {
		argoCd.run('restart', 'Deployment', deployment)
	}
	argoCd.logout()
}

/**
 * Refresh and Sync any applications managed by Kustomize through ArgoCd.
 * This method uses hard refresh to pull all updated manifests from across multiple repos.
 *
 * @param config (Map) - This is split into the below params.
 * @param argoCdUsername (String) MANDATORY ArgoCD Username to authenticate
 * @param argoCdPassword (String) MANDATORY ArgoCD password for the user
 * @param argoCdAppName (Map) MANDATORY ArgoCD appname to work with
 * @param argoCdDomain (String) MANDATORY ArgoCD domain where the server is hosted
 */
def syncKustomizeApp(Map config = [:]) {
	def argoCd = new ArgoCd(this, config.argoCdDomain, config.argoCdAppName)

	argoCd.login(config.argoCdUsername, config.argoCdPassword)
	argoCd.hardRefresh()
	argoCd.sync()
	argoCd.waitForSync()
	argoCd.logout()
}

/**
 * Refresh and Sync Kustomize applications without waiting for sync completion.
 * This method performs a hard refresh to pull updated manifests and initiates sync,
 * but doesn't wait for the sync to complete, allowing for more flexible pipeline workflows.
 *
 * @param config (Map) - This is split into the below params.
 * @param argoCdUsername (String) MANDATORY ArgoCD Username to authenticate
 * @param argoCdPassword (String) MANDATORY ArgoCD password for the user
 * @param argoCdAppName (Map) MANDATORY ArgoCD appname to work with
 * @param argoCdDomain (String) MANDATORY ArgoCD domain where the server is hosted
 */
def updateKustomizeApp(Map config = [:]) {
	// Only waitForSync is the difference between above and this method, by separating deployment
	// from updates, this provides flexibility to add more control here, for example restarts can be added here.
	def argoCd = new ArgoCd(this, config.argoCdDomain, config.argoCdAppName)

	argoCd.login(config.argoCdUsername, config.argoCdPassword)
	argoCd.hardRefresh()
	argoCd.sync()
	argoCd.logout()
}