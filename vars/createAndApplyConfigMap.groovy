import org.global.ParameterValidationUtil

/**
 * Create and apply a config map to the kubernetes cluster
 *
 * @param config (Map) - This is split into the below params, use it inside the map.
 * @param cmName (String) MANDATORY The name of the configmap
 * @param namespace (String) MANDATORY The namespace where to apply the configmap
 * @param data (Map) MANDATORY The data for the configmap as key value pairs
 *
 * @example
 * createAndApplyConfigMap cmName: 'surveysparrow-config-a', namespace: 'surveysparrow', data: [euiDistVersion: '1234']
 */
def call(Map config = [:]) {
  def validOpts = [ 'cmName', 'namespace', 'data' ]

  ParameterValidationUtil.validateParameterMap(validOpts, config, validOpts) // validOpts are used as Mandatory Opts


  def yamlTemplate = [:]
  yamlTemplate['apiVersion'] = 'v1'
  yamlTemplate['kind'] = 'ConfigMap'
  yamlTemplate['metadata'] = [:]
  yamlTemplate['metadata']['name'] = config.cmName
  yamlTemplate['metadata']['namespace'] = config.namespace
  yamlTemplate['data'] = [:]

  config.data.each { key, value -> 
    yamlTemplate['data'][key] = value
  }

  writeYaml file: 'cm-template.yaml', overwrite: true, data: yamlTemplate
  k8s.apply('./cm-template.yaml')
}