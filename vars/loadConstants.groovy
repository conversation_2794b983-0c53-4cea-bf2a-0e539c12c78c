import org.global.CommonConstants

/**
 * Load and return the constants from org-wide global constants, product-wide constants, environment-specific constants.
 * Performs a deep merge of configuration objects, combining nested keys.
 * 
 * @return the config as a HashMap Object
 * 
 * @example
 * variables = loadConstants()
 * echo "${variables.KEY_NAME}"
 */

def call() {
    def variables = [:] // Initialize an empty map

    // Add default static variables
    deepMerge(variables, CommonConstants.DEFAULTS)

    // Add dynamic variables based on organization and environment
    def orgName = env.ORG ?: env.JOB_NAME.split('/')[0].toLowerCase()
    def envName = env.ENVIRONMENT ?: 'staging'

    def orgConstantsClass = "org.${orgName}.CommonConstants"
    try {
        def orgConstants = this.class.classLoader.loadClass(orgConstantsClass)
        deepMerge(variables, orgConstants?.DEFAULTS)
        echo "Loaded organization-specific variables from ${orgConstantsClass}"
    } catch (ClassNotFoundException e) {
        echo "No organization-specific variables found for ${orgConstantsClass}"
    }

    echo "Loading Constants for org: ${orgName}, env: ${envName}"

    def resourcePath = "configs/${orgName}/${envName}.json"
    try {
        def resourceContent = libraryResource(resourcePath)
        def dynamicConfig = readJSON text: resourceContent
        deepMerge(variables, dynamicConfig) // Deep merge dynamic variables
    } catch (Exception e) {
        echo "Error loading dynamic configuration: ${e.message}"
    }

    return variables // Return the complete map of constants
}

/**
 * Performs a deep merge of source map into target map
 * Similar to how Node.js 'config' package works with nested properties
 *
 * @param target The target map to merge into
 * @param source The source map to merge from
 * @return The merged target map
 */
def deepMerge(Map target, Map source) {
    if (source == null) return target
    
    source.each { key, value ->
        if (value instanceof Map && target[key] instanceof Map) {
            // If both are maps, recursively merge them
            deepMerge(target[key], value)
        } else {
            // Otherwise, just set the value
            target[key] = value
        }
    }
    return target
}
