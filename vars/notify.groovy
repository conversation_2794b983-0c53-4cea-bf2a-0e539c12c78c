/**
* Send notifications to slack channels based on status types as specified in constants or config.
* STATUS_EMOJI_MAP, STATUS_COLOR_MAP are few things you would need to use this notifier.
*
* @param status (String) The deployment status as in the STATUS_EMOJI_MAP or STATUS_COLOR_MAP
* @param params (Map) The notification params that your message should contain
* @param variables (Map) The variables to fetch the Maps
* @param config (Map) Optional. Specify if it is a botUser or timestamp. [ botUser: true ]
* @return Object The slack response if botUser is true, otherwise null
*/
Object call(String status, Map params = [:], Map variables = [:], Map config = [:]) {
  params.pipeline = params.pipeline == null ? env.JOB_NAME.split('/').last() : params.pipeline

  List<String> notificationKeysToSkip = ['pipeline', 'channel', 'logs', 'link']
  List<String> statusForCustomMessage = ['APPROVAL']

  String emoji = config.emoji ? config.emoji : variables.STATUS_EMOJI_MAP[status]

  String initialMessage = emoji + ' ' + params.pipeline
  String message = statusForCustomMessage.contains(status)
                ? '*_' + initialMessage + '_*\n\n\n'
                : '*_' + initialMessage + ' ' + status.toLowerCase() + '_*\n\n\n'
  params.each { key, value ->
    if (!notificationKeysToSkip.contains(key)) {
      message += "*${key}*\n `${value}`\n\n"
    }
  }

  if (params.logs != null) {
    message += params.logs
  }

  if (params.link != null) {
    message += params.link
  }

  String color = variables.STATUS_COLOR_MAP[status]
  String channel = params.channel.replaceAll('/', '-')

  if (config.botUser) {
    def slackResponse = slackSend(
      color: color,
      message: message,
      teamDomain: 'surveysparrow',
      channel: channel,
      tokenCredentialId: variables.SLACK_BOT_TOKEN_ID,
      botUser: config.botUser
    )
    return slackResponse
  }

  slackSend(
    color: color,
    message: message,
    teamDomain: 'surveysparrow',
    channel: channel,
    tokenCredentialId: variables.SLACK_TOKEN_ID
  )
  return
}
