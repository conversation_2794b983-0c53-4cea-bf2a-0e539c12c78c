import org.global.MessageUtils

/**
* Send the progress message as seen in #production-deployments channel for deployments
*
* @param config (Map) This is split into the below params
* @param messageType (String) one of the values 'DEPLOYMENT_STARTED', 'DEPLOYMENT_SUCCESSFUL', 'DEPLOYMENT_FAILED', 'DEPLOYMENT_ABORTED'
* @param application (String) The application for which the deployment message is sent
* @return msgTimestamp (String) The message timestamp to send further messages
*/
def call(Map config = [:]) {
  def progressMessage  = MessageUtils.getDeploymentProgressMessage(config.messageType, config.application, config.dc)

  if (!fileExists('slackprogressnotification.py')) {
    loadResourceFile('global/slackprogressnotification.py')
  }

  def msgTimestamp = sh (script: "SLACK_TOKEN=${config.slackBotToken} python3 slackprogressnotification.py -m '${progressMessage}' -p ${config.percentage} -s '${env.STAGE_NAME}' -t ${config.messageTimestamp} -c ${config.channelId} -e ${config.etc} | tr -d '\n'", returnStdout: true)
  return msgTimestamp
}