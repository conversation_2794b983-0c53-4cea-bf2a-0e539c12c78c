import org.global.EcsManager
import org.global.CloudAuthManager
import groovy.json.JsonSlurper

/**
 * ECS utility functions for common ECS operations and health checks.
 * 
 * This file provides utility functions that complement the main ecsDeployment function
 * and EcsManager class, offering convenient methods for ECS service management,
 * health monitoring, and deployment validation.
 */

/**
 * Check the health status of an ECS service
 * 
 * @param config Configuration map
 * @param serviceName (String) MANDATORY ECS service name
 * @param clusterName (String) MANDATORY ECS cluster name
 * @param region (String) MANDATORY AWS region
 * @param accountConfig (Map) Optional cross-account configuration
 * 
 * @return Map Service health information
 * 
 * @example
 * def health = ecsUtils.checkServiceHealth([
 *   serviceName: 'my-api-service',
 *   clusterName: 'production-cluster',
 *   region: 'us-east-1'
 * ])
 */
def checkServiceHealth(Map config = [:]) {
  def requiredParams = ['serviceName', 'clusterName', 'region']
  requiredParams.each { param ->
    if (!config[param]) {
      error("[-] Missing required parameter: ${param}")
    }
  }
  
  try {
    def cloudAuthManager = new CloudAuthManager(this, config.region, config.accountConfig ?: [:])
    def ecsManager = new EcsManager(this, config.region, cloudAuthManager)
    
    cloudAuthManager.authenticateToAWS()
    
    return ecsManager.getServiceHealth(config.clusterName, config.serviceName)
    
  } catch (Exception e) {
    echo "[-] Failed to check service health: ${e.getMessage()}"
    return [healthy: false, error: e.getMessage()]
  }
}

/**
 * List all ECS services in a cluster
 * 
 * @param config Configuration map
 * @param clusterName (String) MANDATORY ECS cluster name
 * @param region (String) MANDATORY AWS region
 * @param accountConfig (Map) Optional cross-account configuration
 * 
 * @return List Service ARNs
 */
def listServices(Map config = [:]) {
  def requiredParams = ['clusterName', 'region']
  requiredParams.each { param ->
    if (!config[param]) {
      error("[-] Missing required parameter: ${param}")
    }
  }
  
  try {
    def cloudAuthManager = new CloudAuthManager(this, config.region, config.accountConfig ?: [:])
    cloudAuthManager.authenticateToAWS()
    
    def listCommand = """
      aws ecs list-services \\
        --cluster ${config.clusterName} \\
        --region ${config.region} \\
        --output json
    """
    
    def result = sh(script: listCommand, returnStdout: true).trim()
    def jsonSlurper = new JsonSlurper()
    def listResult = jsonSlurper.parseText(result)
    
    return listResult.serviceArns ?: []
    
  } catch (Exception e) {
    echo "[-] Failed to list services: ${e.getMessage()}"
    return []
  }
}

/**
 * Get detailed information about ECS services
 * 
 * @param config Configuration map
 * @param clusterName (String) MANDATORY ECS cluster name
 * @param serviceNames (List) MANDATORY List of service names
 * @param region (String) MANDATORY AWS region
 * @param accountConfig (Map) Optional cross-account configuration
 * 
 * @return List Service details
 */
def describeServices(Map config = [:]) {
  def requiredParams = ['clusterName', 'serviceNames', 'region']
  requiredParams.each { param ->
    if (!config[param]) {
      error("[-] Missing required parameter: ${param}")
    }
  }
  
  try {
    def cloudAuthManager = new CloudAuthManager(this, config.region, config.accountConfig ?: [:])
    cloudAuthManager.authenticateToAWS()
    
    def serviceList = config.serviceNames.join(' ')
    def describeCommand = """
      aws ecs describe-services \\
        --cluster ${config.clusterName} \\
        --services ${serviceList} \\
        --region ${config.region} \\
        --output json
    """
    
    def result = sh(script: describeCommand, returnStdout: true).trim()
    def jsonSlurper = new JsonSlurper()
    def describeResult = jsonSlurper.parseText(result)
    
    return describeResult.services ?: []
    
  } catch (Exception e) {
    echo "[-] Failed to describe services: ${e.getMessage()}"
    return []
  }
}

/**
 * Stop all tasks in an ECS service (useful for maintenance)
 * 
 * @param config Configuration map
 * @param serviceName (String) MANDATORY ECS service name
 * @param clusterName (String) MANDATORY ECS cluster name
 * @param region (String) MANDATORY AWS region
 * @param accountConfig (Map) Optional cross-account configuration
 * @param reason (String) Optional reason for stopping tasks
 * 
 * @return Boolean Success status
 */
def stopServiceTasks(Map config = [:]) {
  def requiredParams = ['serviceName', 'clusterName', 'region']
  requiredParams.each { param ->
    if (!config[param]) {
      error("[-] Missing required parameter: ${param}")
    }
  }
  
  try {
    def cloudAuthManager = new CloudAuthManager(this, config.region, config.accountConfig ?: [:])
    cloudAuthManager.authenticateToAWS()
    
    echo "[+] Stopping all tasks for service: ${config.serviceName}"
    
    // First, get all running tasks
    def listTasksCommand = """
      aws ecs list-tasks \\
        --cluster ${config.clusterName} \\
        --service-name ${config.serviceName} \\
        --desired-status RUNNING \\
        --region ${config.region} \\
        --output json
    """
    
    def result = sh(script: listTasksCommand, returnStdout: true).trim()
    def jsonSlurper = new JsonSlurper()
    def listResult = jsonSlurper.parseText(result)
    
    if (listResult.taskArns && !listResult.taskArns.isEmpty()) {
      def reason = config.reason ?: 'Manual stop via Jenkins pipeline'
      
      listResult.taskArns.each { taskArn ->
        def stopCommand = """
          aws ecs stop-task \\
            --cluster ${config.clusterName} \\
            --task ${taskArn} \\
            --reason "${reason}" \\
            --region ${config.region}
        """
        
        sh(script: stopCommand)
        echo "[+] Stopped task: ${taskArn}"
      }
      
      echo "[+] All tasks stopped successfully"
      return true
    } else {
      echo "[+] No running tasks found for service: ${config.serviceName}"
      return true
    }
    
  } catch (Exception e) {
    echo "[-] Failed to stop service tasks: ${e.getMessage()}"
    return false
  }
}

/**
 * Validate ECS deployment configuration
 * 
 * @param config Configuration map to validate
 * @return Map Validation result with errors and warnings
 */
def validateDeploymentConfig(Map config = [:]) {
  def errors = []
  def warnings = []
  
  // Check required parameters
  def requiredParams = ['serviceName', 'clusterName', 'taskDefinitionFamily', 'containerImage', 'region']
  requiredParams.each { param ->
    if (!config[param]) {
      errors << "Missing required parameter: ${param}"
    }
  }
  
  // Validate CPU and memory combinations for Fargate
  if (config.requiresCompatibilities?.contains('FARGATE')) {
    def validCombinations = [
      '256': ['512', '1024', '2048'],
      '512': ['1024', '2048', '3072', '4096'],
      '1024': ['2048', '3072', '4096', '5120', '6144', '7168', '8192'],
      '2048': ['4096', '5120', '6144', '7168', '8192', '9216', '10240', '11264', '12288', '13312', '14336', '15360', '16384'],
      '4096': ['8192', '9216', '10240', '11264', '12288', '13312', '14336', '15360', '16384']
    ]
    
    def cpu = config.cpu?.toString()
    def memory = config.memory?.toString()
    
    if (cpu && memory) {
      if (!validCombinations[cpu]?.contains(memory)) {
        errors << "Invalid CPU/Memory combination for Fargate: ${cpu}/${memory}"
      }
    }
  }
  
  // Validate network configuration
  if (config.networkMode == 'awsvpc' || !config.networkMode) {
    if (!config.subnets || config.subnets.isEmpty()) {
      errors << "Subnets are required for awsvpc network mode"
    }
    if (!config.securityGroups || config.securityGroups.isEmpty()) {
      errors << "Security groups are required for awsvpc network mode"
    }
  }
  
  // Validate deployment percentages
  if (config.minHealthyPercent && config.maxHealthyPercent) {
    if (config.minHealthyPercent > config.maxHealthyPercent) {
      errors << "minHealthyPercent cannot be greater than maxHealthyPercent"
    }
  }
  
  // Check for common issues
  if (config.desiredCount && config.desiredCount > 10) {
    warnings << "High desired count (${config.desiredCount}) - ensure cluster has sufficient capacity"
  }
  
  if (config.deploymentTimeoutMinutes && config.deploymentTimeoutMinutes < 5) {
    warnings << "Short deployment timeout (${config.deploymentTimeoutMinutes} minutes) may cause premature failures"
  }
  
  return [
    valid: errors.isEmpty(),
    errors: errors,
    warnings: warnings
  ]
}

/**
 * Get ECS cluster information
 * 
 * @param config Configuration map
 * @param clusterName (String) MANDATORY ECS cluster name
 * @param region (String) MANDATORY AWS region
 * @param accountConfig (Map) Optional cross-account configuration
 * 
 * @return Map Cluster information
 */
def getClusterInfo(Map config = [:]) {
  def requiredParams = ['clusterName', 'region']
  requiredParams.each { param ->
    if (!config[param]) {
      error("[-] Missing required parameter: ${param}")
    }
  }
  
  try {
    def cloudAuthManager = new CloudAuthManager(this, config.region, config.accountConfig ?: [:])
    cloudAuthManager.authenticateToAWS()
    
    def describeCommand = """
      aws ecs describe-clusters \\
        --clusters ${config.clusterName} \\
        --include STATISTICS \\
        --region ${config.region} \\
        --output json
    """
    
    def result = sh(script: describeCommand, returnStdout: true).trim()
    def jsonSlurper = new JsonSlurper()
    def describeResult = jsonSlurper.parseText(result)
    
    if (describeResult.clusters && !describeResult.clusters.isEmpty()) {
      return describeResult.clusters[0]
    }
    
    return [error: 'Cluster not found']
    
  } catch (Exception e) {
    echo "[-] Failed to get cluster info: ${e.getMessage()}"
    return [error: e.getMessage()]
  }
}

/**
 * Wait for ECS service to reach desired state
 * 
 * @param config Configuration map
 * @param serviceName (String) MANDATORY ECS service name
 * @param clusterName (String) MANDATORY ECS cluster name
 * @param region (String) MANDATORY AWS region
 * @param desiredState (String) Desired state: 'STABLE', 'STOPPED'. Default: 'STABLE'
 * @param timeoutMinutes (Integer) Timeout in minutes. Default: 15
 * @param accountConfig (Map) Optional cross-account configuration
 * 
 * @return Boolean Success status
 */
def waitForServiceState(Map config = [:]) {
  def requiredParams = ['serviceName', 'clusterName', 'region']
  requiredParams.each { param ->
    if (!config[param]) {
      error("[-] Missing required parameter: ${param}")
    }
  }
  
  def desiredState = config.desiredState ?: 'STABLE'
  def timeoutMinutes = config.timeoutMinutes ?: 15
  
  try {
    def cloudAuthManager = new CloudAuthManager(this, config.region, config.accountConfig ?: [:])
    cloudAuthManager.authenticateToAWS()
    
    echo "[+] Waiting for service ${config.serviceName} to reach state: ${desiredState}"
    
    def waitCommand = ""
    switch (desiredState) {
      case 'STABLE':
        waitCommand = """
          aws ecs wait services-stable \\
            --cluster ${config.clusterName} \\
            --services ${config.serviceName} \\
            --region ${config.region}
        """
        break
      case 'STOPPED':
        waitCommand = """
          aws ecs wait services-inactive \\
            --cluster ${config.clusterName} \\
            --services ${config.serviceName} \\
            --region ${config.region}
        """
        break
      default:
        error("[-] Invalid desired state: ${desiredState}")
    }
    
    timeout(time: timeoutMinutes, unit: 'MINUTES') {
      sh(script: waitCommand)
    }
    
    echo "[+] Service reached desired state: ${desiredState}"
    return true
    
  } catch (Exception e) {
    echo "[-] Failed to wait for service state: ${e.getMessage()}"
    return false
  }
}
