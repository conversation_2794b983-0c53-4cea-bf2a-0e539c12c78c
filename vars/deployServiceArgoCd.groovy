import org.global.ArgoCd
import org.global.ParameterValidationUtil

def call(Map config = [:]) {

  def validOpts = [ 'argoCdUsername', 'argoCdPassword', 'argoCdAppName', 'argoCdDomain', 'action' ]
  ParameterValidationUtil.validateParameterMap(validOpts, config)

  switch(config.action) {
    case 'DEPLOY':
      deploy(config)
      break
    case 'CONFIG_UPDATE':
      configUpdate(config)
      break
    case 'RESTART':
      restart(config)
      break
    default:
      error "[-] Unknown option specified for action, valid actions are ['DEPLOY', 'CONFIG_UPDATE', 'RESTART']"
  }
}

def deploy(Map config = [:]) {
  def validOpts = [ 'argoCdUsername', 'argoCdPassword', 'argoCdAppName', 'argoCdDomain' ]
  ParameterValidationUtil.validateParameterMap(validOpts, config)

  def argoCd = new ArgoCd(this, config.argoCdAppName, config.argoCdDomain)

  argoCd.login(config.argoCdUsername, config.argoCdPassword)
  argoCd.sync()
  argoCd.logout()

  echo "[+] Deploy called successfully"
}

def configUpdate(Map config = [:]) {
  def validOpts = [ 'argoCdUsername', 'argoCdPassword', 'argoCdAppName', 'argoCdDomain', 'configMapName', 'deployments' ]
  ParameterValidationUtil.validateParameterMap(validOpts, config)

  def argoCd        = new ArgoCd(this, config.argoCdAppName, config.argoCdDomain)
  def configResource = ":ConfigMap:${config.configMapName}"

  argoCd.login(config.argoCdUsername, config.argoCdPassword)
  argoCd.sync(configResource)

  for (def deployment in deployments) {
    argoCd.run('restart', 'Deployment', deployment)
  }

  argoCd.logout()
}

def restart(Map config = [:]) {
  def validOpts = [ 'argoCdUsername', 'argoCdPassword', 'argoCdAppName', 'argoCdDomain', 'deployments' ]
  ParameterValidationUtil.validateParameterMap(validOpts, config)

  def argoCd = new ArgoCd(this, config.argoCdAppName, config.argoCdDomain)

  argoCd.login(config.argoCdUsername, config.argoCdPassword)
  for (def deployment in deployments) {
    argoCd.run('restart', 'Deployment', deployment)
  }
  argoCd.logout()
}