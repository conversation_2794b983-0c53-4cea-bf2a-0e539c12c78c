/**
 * Pipeline step to get the last successful duration of a job
 * 
 * @param jobName (String) the name of the job to fetch the last successful duration
 * @param averageDeploymentTime (Number) Optional the time to return if last successful is null. (Default: 900000, 15mins)
 *
 * @return lastSuccessfulBuildDuration (Number) the time taken by in ms for the last successful job
 */
def call(String jobName, String averageDeploymentTime = 900000) {
  def job = Jenkins.instance.getItemByFullName(jobName)
  def lastSuccessfulBuild         = job.getLastSuccessfulBuild()
  def lastSuccessfulBuildDuration = lastSuccessfulBuild == null ? averageDeploymentTime : lastSuccessfulBuild?.duration
  return lastSuccessfulBuildDuration
}
