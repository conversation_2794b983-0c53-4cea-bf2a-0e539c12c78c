import org.global.CommonConstants

/**
 * Clones a repository from source control repository using pipeline step git.
 *
 * @param branch The branch to clone.
 * @param repoUrl The repository URL.
 * @param credentialId The Jenkins credentials ID.
 * @return clones the repo in the current working directory.
 */

def cloneRepoWithGit(String branch, String repoUrl, String credentialId) {
    git branch: branch, url: repoUrl, credentialsId: credentialId
}

/**
 * Clones a repository from source control repository using pipeline checkout scmGit().
 *
 * @param branch The branch to clone.
 * @param repoUrl The repository URL.
 * @param credentialId The Jenkins credentials ID.
 * @return clones the repo in the current working directory.
 */
def cloneRepo(String branch, String repoUrl, String credentialId) {
  retry(5) {
    checkout scmGit(
      branches: [[name: branch]],
      extensions: [
        submodule( reference: '', parentCredentials: true, recursiveSubmodules: true )
      ],
      userRemoteConfigs: [
        [
          credentialsId: credentialId,
          url: repoUrl
        ]
      ]
    )
  }
}

/**
 *    Checkout source code from a source branch and merge to target branch
 *
 * @param sourceBranch (String) The source branch to checkout
 * @param targetBranch (String) The target branch to merge the source branch to
 */
def checkoutMergeAndPush(String sourceBranch, String targetBranch) {
    if (sh (script: "git ls-remote | grep refs/heads/${sourceBranch}\$", returnStatus: true) == 0) {
        setGitConfig()
        sh """
        git remote update
        git fetch origin ${sourceBranch}
        git checkout ${sourceBranch}
        git pull origin ${sourceBranch}
        git fetch origin ${targetBranch}
        git checkout ${targetBranch}
        git pull origin ${targetBranch}
        git merge ${sourceBranch}
        git push origin ${targetBranch}
        """
    } else {
        echo "[-] Branch - ${sourceBranch} doesn't exist."
    }
}

/**
 * Commit and push the code to specified branch
 *
 * @param branch (String) The target branch to push the commit to
 * @param message (String) The commit message
 * @param filePath (String) OPTIONAL The specific file to commit (Default - . (all files))
 */
def commitAndPush(String branch, String message, String filePath = ".") {
    setGitConfig()
    try {
        sh "git add $filePath && git commit -m '$message'"
        sh "git pull origin $branch --rebase"
        sh "git push origin HEAD:$branch"
    } catch (Exception e) {
        echo "[-] No changes found to commit"
    }
}

/**
 *    Set the Git Config for username and email from common constants
 */    
def setGitConfig() {
    sh "git config user.email ${CommonConstants.DEFAULTS.GIT_EMAIL}"
    sh "git config user.name  ${CommonConstants.DEFAULTS.GIT_USERNAME}"
}

/**
 *    Get the commit hash from the repository
 *
 * @param useShort (Boolean) Optional - whether to return the short or long commit hash (Default: true)
 * @return commitHash (String) The commit hash in short or long format
 */
def getCommitHash(Boolean useShort = true) {
    def commitHash = sh (script: "git rev-parse ${useShort ? '--short' : ''} HEAD", returnStdout: true).trim()
    return commitHash
}

/**
 * Create a new branch in the repository
 *
 * @param branchName (String) The name of the branch to create
 * @param sourceBranch (String) The source branch to create the new branch from
 */
def createBranch(String branchName, String sourceBranch = "master") {
    sh """
        git fetch origin
        if git rev-parse --verify origin/${branchName} > /dev/null 2>&1; then
            echo "Branch ${branchName} already exists in the repository. Switching to it."
            git checkout ${branchName}
            git pull origin ${branchName}
        else
            echo "Creating new branch ${branchName} in repository."
            git checkout ${sourceBranch}
            git pull origin ${sourceBranch}
            git checkout -b ${branchName}
            git push -u origin ${branchName}
        fi
    """
}

/**
 * Get a list of changed files in the latest commit
 *
 * @return (List) A list of changed files with their status (A=Added, M=Modified, D=Deleted, R=Renamed)
 */
def getChangedFiles() {
    def changedFiles = sh(
        script: 'git show --name-status --pretty=format:',
        returnStdout: true
    ).trim().split('\n')
    return changedFiles
}
