/**
 * Run a kubernetes job from a given YAML file and print logs of the job if needed.
 *
 * @param config (Map) This is split into the below params
 * @param filePath (String) MANDATORY The path of the kubernetes job YAML file
 * @param selector (Map) MANDATORY The kubernetes label selector to identify the pods of the Job E.g. ['migration/type': 'PRE', 'migration/id': "${commit}", 'migration/build': "${env.BUILD_NUMBER}"]
 * @param namespace (String) MANDATORY The namespace in which the Job is running in
 *
 * @param log (Boolean) Optional. Whether to print the logs or not
 * @params logFile (String) Optional. Where to write the log file to
* @param readinessTimeout (int) Optional. The timeout in seconds to wait for the pod to be ready
* @param activeDeadline (int) Optional. The timeout in seconds for the job to complete
 * @param environment (Map) Optional. Key value of environment to substitute in the Job YAML
 */
def call(Map config = [:]) {
  def validOpts = ['filePath', 'selector', 'namespace', 'log', 'logFile', 'environment', 'readinessTimeout', 'activeDeadline']

  String command = ""
  if (config.environment && !(config.environment.isEmpty())) {
    config.environment.each { key, value -> 
      command += "${key}=${value} "
    }
  }

  command += "envsubst < ${config.filePath} > /tmp/job.yaml"
  sh command

  k8s.create("/tmp/job.yaml")


  if (config.log) {
    k8s.liveLogs(
      labelSelector = config.selector,
      namespace = config.namespace,
      readinessTimeout = config.readinessTimeout ?: 5,
      activeDeadline = config.activeDeadline ?: -1
    )
  } else {
   k8s.waitUntilJobCompletion(config.selector, config.namespace, config.activeDeadline ?: -1)
  }

  if (config.logFile) {
    k8s.logToFile(config.selector, config.namespace, config.logFile)
  }

  if(!k8s.hasJobSucceededByLabels(config.selector, config.namespace)){
    error "[-] Job failed"
  }
  
  sh "rm /tmp/job.yaml"
}

