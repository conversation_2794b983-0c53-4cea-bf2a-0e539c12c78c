# ECS Deployment Shared Library Guide

This guide provides comprehensive documentation for using the <PERSON> shared library for AWS ECS deployments.

## Overview

The ECS deployment shared library provides a standardized way to deploy containerized applications to AWS ECS services with support for:

- Cross-account deployments using IAM role assumption
- Multiple deployment strategies (Rolling, Blue/Green)
- Comprehensive monitoring and health checks
- Automatic rollback capabilities
- Integration with existing CloudAuthManager and notification systems

## Quick Start

### Basic ECS Deployment

```groovy
@Library('sparrow-jenkins-shared-library') _

pipeline {
    agent any
    
    stages {
        stage('Deploy to ECS') {
            steps {
                ecsDeployment([
                    serviceName: 'my-api-service',
                    clusterName: 'production-cluster',
                    taskDefinitionFamily: 'my-api-task',
                    containerImage: '************.dkr.ecr.us-east-1.amazonaws.com/my-api:latest',
                    region: 'us-east-1',
                    desiredCount: 3,
                    subnets: ['subnet-12345', 'subnet-67890'],
                    securityGroups: ['sg-abcdef']
                ])
            }
        }
    }
}
```

### Cross-Account Deployment

```groovy
ecsDeployment([
    serviceName: 'my-api-service',
    clusterName: 'production-cluster',
    taskDefinitionFamily: 'my-api-task',
    containerImage: '************.dkr.ecr.us-east-1.amazonaws.com/my-api:v1.2.3',
    region: 'us-east-1',
    accountConfig: [
        crossAccount: true,
        roleArn: 'arn:aws:iam::************:role/ECSDeploymentRole',
        accountId: '************'
    ],
    desiredCount: 5,
    subnets: ['subnet-12345', 'subnet-67890'],
    securityGroups: ['sg-abcdef']
])
```

## Configuration Structure

### Environment Configuration Files

Create environment-specific configuration files following the existing pattern:

```json
{
  "ECR_REGISTRY": {
    "us-east-1": "************.dkr.ecr.us-east-1.amazonaws.com"
  },
  "ACCOUNT_CONFIG": {
    "crossAccount": true,
    "roleArn": "arn:aws:iam::************:role/CrossAccountJenkinsWorker",
    "accountId": "************"
  },
  "ECS": {
    "clusters": {
      "us-east-1": "production-cluster"
    },
    "services": {
      "api-service": {
        "taskDefinitionFamily": "api-service-production",
        "desiredCount": 5,
        "cpu": "512",
        "memory": "1024",
        "subnets": {
          "us-east-1": ["subnet-12345", "subnet-67890"]
        },
        "securityGroups": {
          "us-east-1": ["sg-abcdef"]
        }
      }
    }
  }
}
```

## Available Functions

### Main Deployment Function

#### `ecsDeployment(config)`

The primary function for ECS deployments.

**Required Parameters:**
- `serviceName` (String): ECS service name
- `clusterName` (String): ECS cluster name
- `taskDefinitionFamily` (String): Task definition family name
- `containerImage` (String): Container image URI
- `region` (String): AWS region

**Optional Parameters:**
- `action` (String): 'DEPLOY', 'UPDATE', 'ROLLBACK', 'SCALE' (default: 'DEPLOY')
- `deploymentStrategy` (String): 'ROLLING', 'BLUE_GREEN' (default: 'ROLLING')
- `desiredCount` (Integer): Number of tasks (default: 1)
- `cpu` (String): Task CPU allocation (default: '256')
- `memory` (String): Task memory allocation (default: '512')
- `networkMode` (String): Network mode (default: 'awsvpc')
- `requiresCompatibilities` (List): Launch types (default: ['FARGATE'])
- `subnets` (List): Subnet IDs for awsvpc mode
- `securityGroups` (List): Security group IDs for awsvpc mode
- `environmentVariables` (Map): Environment variables
- `secrets` (Map): Secrets from SSM/Secrets Manager
- `accountConfig` (Map): Cross-account configuration
- `notificationConfig` (Map): Notification settings

### Utility Functions

#### `ecsUtils.checkServiceHealth(config)`

Check the health status of an ECS service.

```groovy
def health = ecsUtils.checkServiceHealth([
    serviceName: 'my-api-service',
    clusterName: 'production-cluster',
    region: 'us-east-1'
])

if (health.healthy) {
    echo "Service is healthy: ${health.runningCount}/${health.desiredCount} tasks running"
} else {
    echo "Service is unhealthy: ${health.error}"
}
```

#### `ecsUtils.listServices(config)`

List all services in an ECS cluster.

```groovy
def services = ecsUtils.listServices([
    clusterName: 'production-cluster',
    region: 'us-east-1'
])

echo "Found ${services.size()} services in cluster"
```

#### `ecsUtils.validateDeploymentConfig(config)`

Validate deployment configuration before deployment.

```groovy
def validation = ecsUtils.validateDeploymentConfig(deploymentConfig)

if (!validation.valid) {
    error("Configuration validation failed: ${validation.errors.join(', ')}")
}

if (validation.warnings) {
    echo "Configuration warnings: ${validation.warnings.join(', ')}"
}
```

## Deployment Strategies

### Rolling Deployment (Default)

Rolling deployments gradually replace old tasks with new ones:

```groovy
ecsDeployment([
    // ... other config
    deploymentStrategy: 'ROLLING',
    maxHealthyPercent: 200,
    minHealthyPercent: 50
])
```

### Blue/Green Deployment

Blue/green deployments create a complete new set of tasks before switching traffic:

```groovy
ecsDeployment([
    // ... other config
    deploymentStrategy: 'BLUE_GREEN',
    maxHealthyPercent: 200,
    minHealthyPercent: 100
])
```

## Advanced Configuration

### Environment Variables and Secrets

```groovy
ecsDeployment([
    // ... other config
    environmentVariables: [
        'ENV': 'production',
        'LOG_LEVEL': 'info',
        'DATABASE_URL': 'postgresql://prod-db:5432/api'
    ],
    secrets: [
        'DATABASE_PASSWORD': 'arn:aws:ssm:us-east-1:************:parameter/db-password',
        'API_SECRET_KEY': 'arn:aws:secretsmanager:us-east-1:************:secret:api-key'
    ]
])
```

### Custom Task and Execution Roles

```groovy
ecsDeployment([
    // ... other config
    executionRoleArn: 'arn:aws:iam::************:role/ecsTaskExecutionRole',
    taskRoleArn: 'arn:aws:iam::************:role/myServiceTaskRole'
])
```

### CloudWatch Logging

```groovy
ecsDeployment([
    // ... other config
    enableLogging: true,
    logGroupName: '/ecs/my-service',
    logRetentionDays: 30
])
```

### Notifications

```groovy
ecsDeployment([
    // ... other config
    notificationConfig: [
        enabled: true,
        channel: '#deployments',
        onSuccess: true,
        onFailure: true
    ]
])
```

## Error Handling and Rollback

### Automatic Rollback

The library supports automatic rollback on deployment failure:

```groovy
ecsDeployment([
    // ... other config
    enableRollback: true,
    deploymentTimeoutMinutes: 15
])
```

### Manual Rollback

```groovy
ecsDeployment([
    // ... other config
    action: 'ROLLBACK'
])
```

## Monitoring and Health Checks

### Service Health Monitoring

```groovy
// Check service health after deployment
def health = ecsUtils.checkServiceHealth([
    serviceName: 'my-api-service',
    clusterName: 'production-cluster',
    region: 'us-east-1'
])

if (!health.healthy) {
    error("Service deployment failed health check")
}
```

### Wait for Service Stability

```groovy
ecsUtils.waitForServiceState([
    serviceName: 'my-api-service',
    clusterName: 'production-cluster',
    region: 'us-east-1',
    desiredState: 'STABLE',
    timeoutMinutes: 10
])
```

## Best Practices

### 1. Use Configuration Files

Store service configurations in environment-specific JSON files rather than hardcoding values in pipelines.

### 2. Validate Configurations

Always validate deployment configurations before deployment:

```groovy
def validation = ecsUtils.validateDeploymentConfig(config)
if (!validation.valid) {
    error("Invalid configuration: ${validation.errors}")
}
```

### 3. Monitor Deployments

Use health checks and monitoring to ensure successful deployments:

```groovy
// Deploy service
ecsDeployment(config)

// Verify health
def health = ecsUtils.checkServiceHealth(config)
if (!health.healthy) {
    error("Deployment health check failed")
}
```

### 4. Use Appropriate Resource Allocation

Choose CPU and memory combinations that are valid for your launch type:

- **Fargate**: Use predefined CPU/memory combinations
- **EC2**: Ensure cluster has sufficient capacity

### 5. Implement Proper IAM Roles

- **Task Execution Role**: Required for pulling images and writing logs
- **Task Role**: Required for application-specific AWS API calls

### 6. Use Secrets Management

Store sensitive information in AWS Systems Manager Parameter Store or AWS Secrets Manager rather than environment variables.

## Troubleshooting

### Common Issues

1. **Invalid CPU/Memory Combinations**: Ensure you're using valid Fargate combinations
2. **Network Configuration**: Verify subnets and security groups are correct
3. **IAM Permissions**: Ensure roles have necessary ECS permissions
4. **Image Pull Errors**: Verify ECR authentication and image existence
5. **Service Discovery**: Check service registration and health checks

### Debug Mode

Enable detailed logging for troubleshooting:

```groovy
ecsDeployment([
    // ... other config
    environmentVariables: [
        'DEBUG': 'true',
        'LOG_LEVEL': 'debug'
    ]
])
```

## Examples

See the `resources/configs/examples/` directory for complete configuration examples:

- `ecs-production.json`: Production environment configuration
- `ecs-staging.json`: Staging environment configuration

## Support

For issues or questions about the ECS deployment shared library, please:

1. Check this documentation
2. Review the example configurations
3. Validate your configuration using `ecsUtils.validateDeploymentConfig()`
4. Contact the DevOps team for assistance
