{"name": "sparrow-jenkins-shared-library", "version": "1.0.0", "description": "Shared libraries in Jenkins Pipelines are reusable pieces of code that can be organized into functions and classes. These libraries allow you to encapsulate common logic, making it easier to maintain and share across multiple pipelines and projects. By utilizing shared libraries, you can enhance the modularity, reusability, and maintainability of your Jenkins Pipeline scripts.", "main": "doumentGenerator.js", "directories": {"doc": "docs"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "generate-docs": "node doumentGenerator.js"}, "author": "", "license": "ISC", "dependencies": {"handlebars": "^4.7.8"}}